"use client";

import React, { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { createUser } from "@/api/userAPI";
import { fetchAllRoles } from "@/api/roleAPI";
import { Role } from "@/api/models/role";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Eye, EyeOff } from "lucide-react";
import { convertNameToNickname } from "@/lib/utils";

const formSchema = z.object({
  name: z.string().min(1, "Tên là bắt buộc"),
  email: z.string().email("Địa chỉ email không hợp lệ"),
  password: z.string().min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
  roleIds: z.array(z.number()).min(1, "Phải chọn ít nhất một vai trò"),
});

type FormData = z.infer<typeof formSchema>;

const Page = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      roleIds: [],
    },
  });

  // Lấy danh sách vai trò cho tùy chọn
  const { data: roles = [] } = useQuery({
    queryKey: ["roles"],
    queryFn: fetchAllRoles,
  });

  console.log("roles", roles);

  // Tạo người dùng mutation
  const createUserMutation = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      toast.success("Tạo người dùng thành công!");
      router.push("/admin/nguoi-dung");
    },
    onError: (error) => {
      toast.error("Tạo người dùng thất bại");
      console.error("Lỗi tạo người dùng:", error);
    },
  });

  const onSubmit = (data: FormData) => {
    createUserMutation.mutate({
      ...data,
      nick_name: convertNameToNickname(data.name),
    });
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Thêm Người Dùng Mới</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Tên</Label>
              <Input id="name" type="text" {...form.register("name")} />
              {form.formState.errors.name && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" {...form.register("email")} />
              {form.formState.errors.email && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.email.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Mật khẩu</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  {...form.register("password")}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {form.formState.errors.password && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.password.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="roleIds">Vai trò</Label>
              <Select
                value={form.watch("roleIds")[0]?.toString() || ""}
                onValueChange={(value) =>
                  form.setValue("roleIds", [parseInt(value)])
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn vai trò" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role: Role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.roleIds && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.roleIds.message}
                </p>
              )}
            </div>

            <div className="flex gap-4 justify-end">
              <Button type="submit" disabled={createUserMutation.isPending}>
                {createUserMutation.isPending
                  ? "Đang tạo..."
                  : "Tạo người dùng"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/nguoi-dung")}
              >
                Quay lại
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default Page;
