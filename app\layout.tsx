import "./globals.css";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { TopNav } from "@/components/top-nav";
import { TooltipProvider } from "@/components/ui/tooltip";
import { SettingsProvider } from "@/contexts/settings-context";
import type React from "react";
import SidebarOption from "@/components/sidebaroption";
import { Toaster } from "sonner";
import { AuthProvider } from "@/hooks/use-auth";
import { Sidebar } from "@/components/sidebar";
import Footer from "@/components/footer";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "TalentVid - Nền tảng đặt video từ người nổi tiếng",
  description: "Đặt video cá nhân từ người nổi tiếng yêu thích của bạn",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <AuthProvider>
            <SettingsProvider>
              <TooltipProvider delayDuration={0}>
                <div className="h-screen flex">
                  <Sidebar />
                  {/* <SidebarOption /> */}
                  <div className="flex-1">
                    <TopNav />
                    <main className="w-full">{children}</main>
                    <Footer />
                  </div>
                </div>
              </TooltipProvider>
            </SettingsProvider>
          </AuthProvider>
        </ThemeProvider>
        <Toaster position="top-right" richColors duration={3000} />
      </body>
    </html>
  );
}
