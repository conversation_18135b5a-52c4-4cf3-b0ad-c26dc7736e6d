import { Star } from "lucide-react"

interface ReviewCardProps {
  name: string
  rating: number
  review: string
}

export function ReviewCard({ name, rating, review }: ReviewCardProps) {
  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-bold text-sm">{name}</h3>
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star key={i} className={`h-3 w-3 ${i < rating ? "fill-yellow-500 stroke-none" : "stroke-gray-400"}`} />
          ))}
        </div>
      </div>
      <p className="text-sm text-gray-400 line-clamp-4">{review}</p>
    </div>
  )
}
