import { useState, useEffect, useMemo } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { Category } from '@/api/models/category'
import { getAdminCategoryColumns } from './columns'
import { deleteCategory, getAllCategories } from '@/api/categoryAPI'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { Button } from '../ui/button'
import CreateCategoryForm from '../create-category-form'
import { getErrorMessage } from '@/lib/utils'
import { ActionConstants, ActionConstantsType } from '@/lib/action-constants'
import { toast } from 'sonner'
import { Input } from '../ui/input'

export default function AdminCategoryTable() {
  const [data, setData] = useState<Category[]>([]);
  const [currentItem, setCurrentItem] = useState<Category | null>(null);
  const [actionMode, setActionMode] = useState<ActionConstantsType>(ActionConstants.CREATE);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  const fetchData = async () => {
    try {
      const response = await getAllCategories();
      setData(response);
      setTotalItems(response.length);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải danh mục');
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm])

  const [open, setOpen] = useState(false);

  const deleteFunc = async (id: number) => {
    try {
      await deleteCategory(id);
      toast.success('Xóa danh mục thành công');
      setData((prev) => prev.filter((item) => item.id !== id));
      setTotalItems((prev) => prev - 1);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể xóa danh mục');
    }
  }

  const columns = useMemo(() => getAdminCategoryColumns({
    deleteFunction: deleteFunc,
    viewFunction: (id: number) => {
      const category = data.find(item => item.id === id);
      if (category) {
        setCurrentItem(category);
        setActionMode(ActionConstants.VIEW);
        setOpen(true);
      }
    },
    editFunction: (id: number) => {
      const category = data.find(item => item.id === id);
      if (category) {
        setCurrentItem(category);
        setActionMode(ActionConstants.UPDATE);
        setOpen(true);
      }
    }
  }), [deleteFunc])

  const toolbarComponent = (
    <div className="flex items-center space-x-2">
      <div>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger>
            <Button variant="outline" className="h-8" onClick={() => {
              setCurrentItem(null);
              setActionMode(ActionConstants.CREATE);
              setOpen(true);
            }}>
              <span className="text-sm">Thêm danh mục</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
              <DialogHeader>
                <DialogTitle>{actionMode === ActionConstants.VIEW ? 'Xem chi tiết' : actionMode === ActionConstants.CREATE ? 'Thêm danh mục' : 'Cập nhật danh mục'}</DialogTitle>
              </DialogHeader>
              <CreateCategoryForm onComplete={(category) => {
                if (actionMode === ActionConstants.UPDATE) {
                  setData((prev) => prev.map(item => item.id === category.id ? category : item));
                } else if (actionMode === ActionConstants.CREATE) {
                  setData((prev) => [...prev, category]);
                  setTotalItems((prev) => prev + 1);
                }
                setOpen(false);
              }} 
              category={currentItem}
              action = {actionMode} />
            </DialogContent>
        </Dialog>
      </div>

      <div>
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8 w-[150px] lg:w-[250px] p-2 rounded-sm"
          />
        </div>
      </div>
    </div>
  )
  return (
      <div className='flex flex-col'>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Danh sách danh mục</h1>
        </div>
        <DataTable
          columns={columns}
          data={data}
          manualPagination={true}
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          onSearchChange={setSearchTerm}
          toolbarComponent={toolbarComponent}
        />
      </div>
  )
}