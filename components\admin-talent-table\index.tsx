import { useState, useEffect, useMemo } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs'
import { AdminTalentOverview, approveTalent, getAllTalents, getTalents, rejectTalent } from '@/api/adminTalentAPI'
import { User } from '@/api/models/user'
import { toast } from 'sonner'
import { getErrorMessage } from '@/lib/utils'
import { getAdminTalentColumns } from './columns'
import { ViewTalentForm } from '../view-talent-form'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { ActionConstants } from '@/lib/action-constants'
import { DataTableSkeleton } from '../shared/skeleton/skeleton-table'

export default function AdminTalentTable() {
  const [data, setData] = useState<User[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [talentStatus, setTalentStatus] = useState('tat-ca');
  const [open, setOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const response = await getTalents({
        page: currentPage,
        limit: pageSize,
        name: searchTerm || undefined,
        status: talentStatus === 'tat-ca' ? undefined : talentStatus as 'active' | 'inactive' | 'pending',
      });
      setData(response.data);
      setTotalItems(response.meta.total);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải danh sách người dùng');
    } finally {
      setIsLoading(false);
    }
  };


  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm, talentStatus])

  const columns = useMemo(() => getAdminTalentColumns({
    approveFunction: async (id: number) => {
      try {
        const response = await approveTalent(id);
        toast.success('Duyệt người dùng thành công');
        setData((prev) => prev.map((item) => item.id === id ? response : item));
      } catch (error) {
        toast.error(getErrorMessage(error) || 'Không thể duyệt người dùng');
      }
    },
    disableFunction: async (id: number) => {
      try {
        const response = await rejectTalent(id);
        toast.success('Khoá người dùng thành công');
        setData((prev) => prev.map((item) => item.id === id ? response : item));
      } catch (error) {
        toast.error(getErrorMessage(error) || 'Không thể khoá người dùng');
      }
    },
    viewFunction: (id: number) => {
      const talent = data.find(item => item.id === id);
      if (talent) {
        setCurrentItem(talent);
        setOpen(true);
      }
    },

  }), [data]);

  const toolbarComponent = (
    <div className="flex items-center justify-between">
      <Tabs defaultValue="tat-ca" value={talentStatus} onValueChange={setTalentStatus}>
        <TabsList className="flex flex-wrap gap-2">
          <TabsTrigger value="tat-ca">Tất cả</TabsTrigger>
          <TabsTrigger value="pending">Chờ duyệt</TabsTrigger>
          <TabsTrigger value="active">Đã duyệt</TabsTrigger>
          <TabsTrigger value="inactive">Bị khoá</TabsTrigger>
        </TabsList>
      </Tabs>
      <div>
        <div className="flex flex-1 items-center space-x-2">
          <input
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8 w-[150px] lg:w-[250px] p-2 rounded-sm"
          />
        </div>
      </div>
    </div>
  )
  return (
    <div className='flex flex-col'>
      <div>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Xem chi tiết</DialogTitle>
            </DialogHeader>
            <ViewTalentForm
              action={ActionConstants.VIEW}
              talent={currentItem || undefined}
              onClose={() => {
                setOpen(false);
                setCurrentItem(null);
              }}
            />
          </DialogContent>
        </Dialog>
      </div>
      <div className='mb-2 flex items-center justify-between space-y-2'>
        <h1 className='text-2xl font-bold tracking-tight'>Danh sách đối tác</h1>
      </div>
      {isLoading ? (
        <DataTableSkeleton />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          manualPagination={true}
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          onSearchChange={setSearchTerm}
          toolbarComponent={toolbarComponent}
        />
      )}

    </div>
  )
}
