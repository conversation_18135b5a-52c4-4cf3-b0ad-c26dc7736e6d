import api from "./axios"
import { Paginator } from "./models/paginator"
import { User } from "./models/user"

export interface AdminTalentOverview {
  id: string
  fullName: string
  email: string
  phone?: string
  category: string
  status: 'active' | 'inactive' | 'banned' | 'pending_approval'
  averageRating: number
  joinedDate: string
}

export const getTalents = async (params: {
  page?: number;
  limit?: number;
  categoryId?: number;
  price?: number;
  name?: string;
  status?: 'active' | 'inactive' | 'pending';
}): Promise<Paginator<User>> => {
  const response = await api.get<Paginator<User>>('/talents', { params });
  return response.data;
};

export const getAllTalents = async (): Promise<User[]> => {
  const response = await api.get<User[]>('/talents/all');
  return response.data;
};

export const getTop10Talents = async (): Promise<User[]> => {
  const response = await api.get<User[]>('/talents/top10');
  return response.data;
};

export const getTalentsByBusinessId = async (businessId: number): Promise<User[]> => {
  const response = await api.get<User[]>(`/talents/talent-by-business-id/${businessId}`);
  return response.data;
};

export const getTalentById = async (id: number): Promise<User> => {
  const response = await api.get<User>(`/talents/${id}`);
  return response.data;
};

export const getTalentBySlug = async (slug: string): Promise<User> => {
  const response = await api.get<User>(`/talents/slug/${slug}`);
  return response.data;
};

export const updateTalent = async (id: number, data: Partial<User>): Promise<User> => {
  const response = await api.patch<User>(`/talents/${id}`, data);
  return response.data;
};

export const deleteTalent = async (id: number): Promise<void> => {
  await api.delete(`/talents/${id}`);
};

export const approveTalent = async (id: number): Promise<User> => {
  const response = await api.patch<User>(`/talents/${id}/approve`);
  return response.data;
};

export const rejectTalent = async (id: number): Promise<User> => {
    const response = await api.patch<User>(`/talents/${id}/reject`);
    return response.data;
};