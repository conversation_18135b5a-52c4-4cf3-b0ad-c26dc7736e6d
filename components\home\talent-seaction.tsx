import React from "react"
import { getTop10Talents } from "@/api/adminTalentAPI"
import { TalentCard } from "../user-home/talent-card"
import Link from "next/link"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { Star } from "lucide-react"
import Image from "next/image"

export default async function TalentSection() {
  const talents = await getTop10Talents()
  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Top 10 on Cameo</h2>
          <Link href="/talent" className="text-sm">
            View all
          </Link>
        </div>

        <Carousel className="w-full">
          <CarouselContent>
            {talents.map((talent: any, index: number) => (
              <CarouselItem
                key={talent.id}
                className="md:basis-1/5 lg:basis-1/6"
              >
                <Link href={`/talent/${talent.nick_name}`} className="min-w-[200px] max-w-[200px]">
                  <div className="relative rounded-lg overflow-hidden">
                    <div className="absolute top-2 left-2 bg-black/50 rounded-full w-6 h-6 flex items-center justify-center text-xs">
                      {index + 1}
                    </div>
                    <Image
                      src={talent.avatar || "/placeholder.svg"}
                      alt={talent.name}
                      width={200}
                      height={300}
                      className="w-full aspect-[2/3] object-cover"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                      <h3 className="font-bold text-sm text-white">{talent.name}</h3>
                      <p className="text-xs text-gray-300">{talent.job}</p>
                    </div>
                  </div>
                  <div className="mt-2 flex justify-between items-center">
                    <span className="font-bold text-sm">${(talent.price / 100).toFixed(0)}+</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-500 stroke-none" />
                      <span className="text-xs">4.9</span>
                      <span className="text-xs text-gray-400">(100)</span>
                    </div>
                  </div>
                </Link>
              </CarouselItem>
            ))}

          </CarouselContent>
          {/* <CarouselPrevious />
          <CarouselNext /> */}
        </Carousel>
      </div>
    </section>
  )
}
