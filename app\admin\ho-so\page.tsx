"use client";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { User, Mail, Phone, Upload, X } from "lucide-react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getProfile, updateProfile } from "@/api/userAPI";
import _ from "lodash";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";

export type ProfileFormData = {
  name: string;
  email: string;
  phone: string;
  avatar?: File;
};

const defaultValues: ProfileFormData = {
  name: "",
  email: "",
  phone: "",
};

const Page = () => {
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isChangeAvt, setIsChangeAvt] = useState(false)
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ProfileFormData>({
    defaultValues,
  });

  const { updateUser } = useAuth();

  const { data, refetch } = useQuery({
    queryKey: ["getProfile"],
    queryFn: getProfile,
  });

  const { mutate, isPending } = useMutation({
    mutationKey: ["update-profile"],
    mutationFn: updateProfile,
    onSuccess: (data) => {
      toast.success("Cập nhật thông tin thành công !");
      updateUser(data);
      refetch();
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChangeAvt(true)
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File quá lớn. Vui lòng chọn file nhỏ hơn 5MB");
        return;
      }
      
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        toast.error("Chỉ chấp nhận file ảnh (JPEG, PNG, GIF)");
        return;
      }

      setSelectedFile(file);
      setValue("avatar", file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeAvatar = () => {
    setSelectedFile(null);
    setAvatarPreview(null);
    setValue("avatar", undefined);
  };

  const onSubmit = async (data: ProfileFormData) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("email", data.email);
    formData.append("phone", data.phone);
    
    if (selectedFile) {
      formData.append("avatar", selectedFile);
    }

    mutate(formData as any);
  };

  useEffect(() => {
    if (data) {
      reset(data as any);
      if (data.avatar) {        
        setAvatarPreview(data.avatar);
      }
    }
  }, [JSON.stringify(data)]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-2">
      <Card className="w-full max-w-lg shadow-xl border-0">
        <CardHeader className="text-center">
          <div className="flex flex-col items-center gap-2">
            <div className="relative">
              <div className="bg-blue-100 rounded-full p-3 mb-2">
                {avatarPreview ? (
                  <img
                    src={avatarPreview}
                    alt="Avatar"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <User className="w-8 h-8 text-blue-600" />
                )}
              </div>
              {(avatarPreview && isChangeAvt) && (
                <button
                  type="button"
                  onClick={removeAvatar}
                  className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition"
                >
                  <X className="w-3 h-3" />
                </button>
              )}
            </div>
            <CardTitle className="text-3xl font-bold">Cập nhật hồ sơ</CardTitle>
            <CardDescription className="text-base text-gray-500">
              Cập nhật thông tin cá nhân của bạn
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <Label
                htmlFor="avatar"
                className="mb-1 flex items-center gap-2 font-medium"
              >
                <Upload className="w-4 h-4 text-blue-500" /> Ảnh đại diện
              </Label>
              <div className="mt-1">
                <Input
                  id="avatar"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById("avatar")?.click()}
                  className="w-full"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Chọn ảnh đại diện
                </Button>
                {selectedFile && (
                  <p className="text-xs text-gray-500 mt-1">
                    Đã chọn: {selectedFile.name}
                  </p>
                )}
              </div>
            </div>
            <div>
              <Label
                htmlFor="name"
                className="mb-1 flex items-center gap-2 font-medium"
              >
                <User className="w-4 h-4 text-blue-500" /> Họ và tên
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Nhập họ và tên"
                {...register("name", { required: "Vui lòng nhập họ và tên" })}
                className={`mt-1 ${
                  errors.name ? "border-red-400 focus:ring-red-400" : ""
                }`}
              />
              {errors.name && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>
            <div>
              <Label
                htmlFor="email"
                className="mb-1 flex items-center gap-2 font-medium"
              >
                <Mail className="w-4 h-4 text-blue-500" /> Email
              </Label>
              <Input
                id="email"
                type="email"
                disabled
                placeholder="Nhập email"
                {...register("email", {
                  required: "Vui lòng nhập email",
                  pattern: {
                    value: /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/,
                    message: "Email không hợp lệ",
                  },
                })}
                className={`mt-1 ${
                  errors.email ? "border-red-400 focus:ring-red-400" : ""
                }`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>
            <div>
              <Label
                htmlFor="phone"
                className="mb-1 flex items-center gap-2 font-medium"
              >
                <Phone className="w-4 h-4 text-blue-500" /> Số điện thoại
              </Label>
              <Input
                id="phone"
                type="tel"
                placeholder="Nhập số điện thoại"
                {...register("phone", {
                  required: "Vui lòng nhập số điện thoại",
                  pattern: {
                    value: /^[0-9]{9,11}$/,
                    message: "Số điện thoại không hợp lệ",
                  },
                })}
                className={`mt-1 ${
                  errors.phone ? "border-red-400 focus:ring-red-400" : ""
                }`}
              />
              {errors.phone && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.phone.message}
                </p>
              )}
            </div>
            <Button
              type="submit"
              disabled={isPending}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded transition"
              size="lg"
            >
              {isPending ? (
                <span className="flex items-center justify-center gap-2">
                  <svg
                    className="animate-spin h-5 w-5 text-white"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8z"
                    />
                  </svg>
                  Đang cập nhật...
                </span>
              ) : (
                "Cập nhật"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default Page;
