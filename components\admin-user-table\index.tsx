import { useState, useEffect, useMemo } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { getAdminUserColumns } from './columns'
import { getUsers, lockUser, unlockUser } from '@/api/userAPI'
import { User } from '@/api/models/user'
import { toast } from 'sonner'
import { getErrorMessage } from '@/lib/utils'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { Button } from '../ui/button'
import { ViewTalentForm } from '../view-talent-form'
import { ActionConstants } from '@/lib/action-constants'
import Link from 'next/link'
import Filter from './filter'

export default function UserTable() {
  const [data, setData] = useState<User[]>([])
  const [totalItems, setTotalItems] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('');
  const [open, setOpen] = useState(false)
  const [currentItem, setCurrentItem] = useState<User | null>(null)

  const fetchData = async () => {
    try {
      const response = await getUsers(currentPage, pageSize);
      setData(response.data);
      setTotalItems(response.meta.total);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải danh sách người dùng');
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize]);

  const columns = useMemo(() => getAdminUserColumns({
    approveFunction: async (id: number) => {
      try {
        await unlockUser(id);
        toast.success('Kích hoạt người dùng thành công');
        setData((prev) => prev.map((item) => item.id === id ? { ...item, status: 'active' } : item));
      } catch (error) {
        toast.error(getErrorMessage(error) || 'Không thể kích hoạt người dùng');
      }
    },
    viewFunction: (id: number) => {
      const user = data.find(item => item.id === id);
      if (user) {
        setCurrentItem(user);
        setOpen(true);
      }
    },
    disableFunction: async (id: number) => {
      try {
        await lockUser(id);
        toast.success('Vô hiệu hóa người dùng thành công');
        setData((prev) => prev.map((item) => item.id === id ? { ...item, status: 'inactive' } : item));
      } catch (error) {
        toast.error(getErrorMessage(error) || 'Không thể vô hiệu hóa người dùng');
      }
    }
  }), [data])

  const toolbarComponent = (
    <div>
      <Link href='/admin/nguoi-dung/them-nguoi-dung' className='mt-4'>
        <Button variant="outline" className="h-8">
          Thêm người dùng
        </Button>
      </Link>
    </div>
  )
  return (
    <div className='flex flex-col'>
      <div className='mb-2 flex items-center justify-between space-y-2'>
        <h1 className='text-2xl font-bold tracking-tight'>Danh sách người dùng</h1>
      </div>
      <div>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Xem chi tiết</DialogTitle>
            </DialogHeader>
            <ViewTalentForm
              action={ActionConstants.VIEW}
              talent={currentItem || undefined}
              onClose={() => {
                setOpen(false);
                setCurrentItem(null);
              }}
            />
          </DialogContent>
        </Dialog>
      </div>
      <DataTable
        columns={columns}
        data={data}
        manualPagination={true}
        totalItems={totalItems}
        pageSize={pageSize}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        onSearchChange={setSearchTerm}
        toolbarComponent={toolbarComponent}
      />
    </div>
  )
}