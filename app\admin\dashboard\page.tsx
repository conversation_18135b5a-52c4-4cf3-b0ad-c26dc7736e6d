"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Users, ShoppingBag, DollarSign, AlertTriangle, Clock, Star } from "lucide-react"
import Link from "next/link"

// Mock data for admin dashboard
const stats = {
  totalUsers: 12450,
  totalTalents: 156,
  totalOrders: 3420,
  totalRevenue: 2850000000,
  pendingComplaints: 8,
  pendingApprovals: 12,
  monthlyGrowth: 15.2,
}

const recentActivity = [
  {
    id: "1",
    type: "new_user",
    user: "Nguyễn Văn A",
    action: "đã đăng ký tài khoản",
    time: "5 phút trước",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=user1",
  },
  {
    id: "2",
    type: "new_order",
    user: "Trần Thị B",
    action: "đã đặt video từ Lê Văn C",
    time: "15 phút trước",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=user2",
  },
  {
    id: "3",
    type: "complaint",
    user: "Phạm Văn D",
    action: "đã gửi khiếu nại đơn hàng #ord-123",
    time: "30 phút trước",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=user3",
  },
  {
    id: "4",
    type: "talent_application",
    user: "Hoàng Thị E",
    action: "đã nộp đơn trở thành talent",
    time: "1 giờ trước",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=user4",
  },
]

const pendingApprovals = [
  {
    id: "app-001",
    name: "Nguyễn Văn F",
    type: "Talent",
    category: "Ca sĩ",
    submittedAt: "2023-07-15",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent1",
  },
  {
    id: "app-002",
    name: "Trần Thị G",
    type: "Business",
    category: "Công ty giải trí",
    submittedAt: "2023-07-14",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=business1",
  },
  {
    id: "app-003",
    name: "Lê Văn H",
    type: "Talent",
    category: "Diễn viên",
    submittedAt: "2023-07-13",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent2",
  },
]

const recentComplaints = [
  {
    id: "comp-001",
    orderId: "ord-456",
    customer: "Phạm Thị I",
    talent: "Nguyễn Văn J",
    reason: "Video không đúng yêu cầu",
    status: "pending",
    submittedAt: "2023-07-15",
  },
  {
    id: "comp-002",
    orderId: "ord-789",
    customer: "Hoàng Văn K",
    talent: "Trần Thị L",
    reason: "Chất lượng video kém",
    status: "investigating",
    submittedAt: "2023-07-14",
  },
]

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Dashboard Quản trị</h1>
        <p className="text-muted-foreground">Tổng quan hệ thống và quản lý hoạt động</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng người dùng</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+{stats.monthlyGrowth}% so với tháng trước</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng Talent</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTalents}</div>
            <p className="text-xs text-muted-foreground">{stats.pendingApprovals} đơn chờ duyệt</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng đơn hàng</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Tất cả thời gian</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng doanh thu</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
                notation: "compact",
              }).format(stats.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">Tất cả thời gian</p>
          </CardContent>
        </Card>
      </div>

      {/* Alert Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khiếu nại chờ xử lý</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingComplaints}</div>
            <p className="text-xs text-orange-600">Cần xử lý ngay</p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đơn chờ duyệt</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.pendingApprovals}</div>
            <p className="text-xs text-blue-600">Talent & Business</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Hoạt động gần đây</CardTitle>
              <CardDescription>Theo dõi hoạt động của người dùng trên hệ thống</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4">
                    <Avatar>
                      <AvatarImage src={activity.avatar || "/placeholder.svg"} alt={activity.user} />
                      <AvatarFallback>{activity.user.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">{activity.user}</span> {activity.action}
                      </p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                    <Badge
                      variant={
                        activity.type === "complaint"
                          ? "destructive"
                          : activity.type === "talent_application"
                            ? "secondary"
                            : "default"
                      }
                    >
                      {activity.type === "new_user" && "Người dùng mới"}
                      {activity.type === "new_order" && "Đơn hàng mới"}
                      {activity.type === "complaint" && "Khiếu nại"}
                      {activity.type === "talent_application" && "Đơn ứng tuyển"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Khiếu nại gần đây</CardTitle>
              <CardDescription>Xử lý khiếu nại từ người dùng</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentComplaints.map((complaint) => (
                  <div key={complaint.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">#{complaint.orderId}</h3>
                        <Badge variant={complaint.status === "pending" ? "destructive" : "secondary"}>
                          {complaint.status === "pending" ? "Chờ xử lý" : "Đang điều tra"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {complaint.customer} khiếu nại {complaint.talent}
                      </p>
                      <p className="text-sm">{complaint.reason}</p>
                      <p className="text-xs text-muted-foreground mt-1">{complaint.submittedAt}</p>
                    </div>
                    <Button size="sm" asChild>
                      <Link href={`/admin/khieu-nai/${complaint.id}`}>Xử lý</Link>
                    </Button>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button className="w-full" variant="outline" asChild>
                  <Link href="/admin/khieu-nai">Xem tất cả khiếu nại</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Đơn chờ duyệt</CardTitle>
              <CardDescription>Duyệt đơn ứng tuyển Talent & Business</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pendingApprovals.map((application) => (
                  <div key={application.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={application.avatar || "/placeholder.svg"} alt={application.name} />
                        <AvatarFallback>{application.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-sm">{application.name}</p>
                        <p className="text-xs text-muted-foreground">{application.category}</p>
                        <Badge variant="outline" className="text-xs">
                          {application.type}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button size="sm" variant="outline">
                        Duyệt
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button className="w-full" variant="outline" asChild>
                  <Link href="/admin/partner">Xem tất cả</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Hành động nhanh</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" asChild>
                <Link href="/admin/khieu-nai">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Xử lý khiếu nại
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/admin/partner">
                  <Users className="mr-2 h-4 w-4" />
                  Duyệt đối tác
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/admin/don-hang">
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Quản lý đơn hàng
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/admin/nguoi-dung">
                  <Users className="mr-2 h-4 w-4" />
                  Quản lý người dùng
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Thống kê hệ thống</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Tỷ lệ hoàn thành đơn</span>
                  <span className="font-medium">94.2%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Đánh giá trung bình</span>
                  <span className="font-medium">4.6/5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Thời gian phản hồi</span>
                  <span className="font-medium">18h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Tỷ lệ khiếu nại</span>
                  <span className="font-medium">2.1%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
