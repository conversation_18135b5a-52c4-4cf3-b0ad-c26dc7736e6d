import { IconMoneybag } from "@tabler/icons-react";
import { AlertTriangle, CheckCircle, Clock, Video, XCircle } from "lucide-react";

export const OrderStatusConsts = {
  "pending": {
    label: "Ch<PERSON> xử lý",
    color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    icon: Clock,
  },
  "rejected": {
    label: "Đã từ chối",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: XCircle,
  },
  "paid" : {
    label: "Đã thanh toán",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: IconMoneybag,
  },
  "cancelled" : {
    label: "Đã hủy",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: XCircle,
  },
  "processing": {
    label: "<PERSON>ang xử lý",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: Video,
  },
  "sent_video": {
    label: "Đã gửi",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    icon: CheckCircle,
  },
  "completed": {
    label: "Hoàn thành",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
  },
  "complaint": {
    label: "Khiếu nại",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: AlertTriangle,
  },
  "resolving": {
    label: "Đang giải quyết",
    color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    icon: AlertTriangle,
  },
  "refunded": {
    label: "Đã hoàn tiền",
    color: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
    icon: XCircle,
  },
} as const;

export type OrderStatusKey = keyof typeof OrderStatusConsts;