import { useState, useEffect } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { toast } from '@/components/ui/use-toast'
import { adminTableColumns } from './columns'
import { getAdminOrders, OrderAdminDto } from '@/api/adminOrderAPI'
import { Tabs, TabsList, TabsTrigger } from '../ui/tabs'

export default function UserTable() {
  const [data, setData] = useState<OrderAdminDto[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderStatus, setOrderStatus] = useState('tat-ca');

  const fetchData = async () => {
    try {
      const response = await getAdminOrders({
        page: currentPage,
        limit: pageSize,
        search: searchTerm
      })
      setData(response.orders)
      setTotalItems(response.totalItems)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch data',
        variant: 'destructive'
      })
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm])

  const toolbarComponent = (
    <div className="flex items-center justify-between">
       <Tabs defaultValue="tat-ca" value={orderStatus} onValueChange={setOrderStatus}>
        <TabsList className="flex flex-wrap gap-2">
          <TabsTrigger value="tat-ca">Tất cả</TabsTrigger>
          <TabsTrigger value="cho-xu-ly">Chờ xử lý</TabsTrigger>
          <TabsTrigger value="dang-xu-ly">Đang xử lý</TabsTrigger>
          <TabsTrigger value="da-gui">Đã gửi</TabsTrigger>
          <TabsTrigger value="hoan-thanh">Hoàn thành</TabsTrigger>
          <TabsTrigger value="khieu-nai">Khiếu nại</TabsTrigger>
        </TabsList>
      </Tabs>
      <div>
        <div className="flex flex-1 items-center space-x-2">
          <input
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8 w-[150px] lg:w-[250px] p-2 rounded-sm"
          />
        </div>
      </div>
    </div>
  )
  return (
      <div className='flex flex-col'>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Danh sách đơn hàng</h1>
        </div>
        <DataTable
          columns={adminTableColumns}
          data={data}
          manualPagination={true}
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          onSearchChange={setSearchTerm}
          toolbarComponent={toolbarComponent}
        />
      </div>
  )
}