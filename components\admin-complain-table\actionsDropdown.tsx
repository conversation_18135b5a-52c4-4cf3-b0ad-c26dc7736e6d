import { useState } from "react";
import {
  Complaint,
  priorityComplaint,
  resolveComplaint,
} from "@/api/complaintsAPI";
import { ComplaintStatus, ComplaintPriority } from "./index";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu";
import ComplaintDetailModal from "./modalDetail";

const ActionsDropdown = ({ complaint }: { complaint: Complaint }) => {
  const [openModal, setOpenModal] = useState(false);
  const queryClient = useQueryClient();

  const { mutate: mutateStatus } = useMutation({
    mutationKey: ["resolve"],
    mutationFn: (params: { id: number; status: ComplaintStatus }) =>
      resolveComplaint(params.id, { status: params.status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["complaints"] });
    },
  });

  const { mutate: mutatePriority } = useMutation({
    mutationKey: ["updatePriority"],
    mutationFn: (params: { id: number; priority: ComplaintPriority }) =>
      priorityComplaint(params.id, { priority: params.priority }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["complaints"] });
    },
  });

  const handleStatusUpdate = (status: ComplaintStatus) => {
    mutateStatus({ id: complaint.id, status });
  };

  const handlePriorityUpdate = (priority: ComplaintPriority) => {
    mutatePriority({ id: complaint.id, priority });
  };

  const getStatusLabel = (status: ComplaintStatus) => {
    switch (status) {
      case ComplaintStatus.PENDING:
        return "Chờ xử lý";
      case ComplaintStatus.INVESTIGATING:
        return "Đang điều tra";
      case ComplaintStatus.RESOLVED:
        return "Đã giải quyết";
      case ComplaintStatus.REJECTED:
        return "Đã từ chối";
      default:
        return status;
    }
  };

  const getStatusColor = (status: ComplaintStatus) => {
    switch (status) {
      case ComplaintStatus.PENDING:
        return "text-yellow-600";
      case ComplaintStatus.INVESTIGATING:
        return "text-blue-600";
      case ComplaintStatus.RESOLVED:
        return "text-green-600";
      case ComplaintStatus.REJECTED:
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getPriorityLabel = (priority: ComplaintPriority) => {
    switch (priority) {
      case ComplaintPriority.LOW:
        return "Thấp";
      case ComplaintPriority.MEDIUM:
        return "Trung bình";
      case ComplaintPriority.HIGH:
        return "Cao";
      case ComplaintPriority.URGENT:
        return "Khẩn cấp";
      default:
        return priority;
    }
  };

  const getPriorityColor = (priority: ComplaintPriority) => {
    switch (priority) {
      case ComplaintPriority.LOW:
        return "text-gray-500";
      case ComplaintPriority.MEDIUM:
        return "text-yellow-500";
      case ComplaintPriority.HIGH:
        return "text-orange-500";
      case ComplaintPriority.URGENT:
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Mở menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Hành động</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() =>
              navigator.clipboard.writeText(complaint.id.toString())
            }
          >
            Sao chép mã khiếu nại
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setOpenModal(true)}>
            Xem chi tiết
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>Cập nhật trạng thái</DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              {Object.values(ComplaintStatus).map((status) => (
                <DropdownMenuItem
                  key={status}
                  onClick={() => handleStatusUpdate(status)}
                  className={`${getStatusColor(status)} ${
                    complaint.status === status ? "bg-gray-100" : ""
                  }`}
                >
                  {getStatusLabel(status)}
                  {complaint.status === status && " (Hiện tại)"}
                </DropdownMenuItem>
              ))}
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              Cập nhật mức độ ưu tiên
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              {Object.values(ComplaintPriority).map((priority) => (
                <DropdownMenuItem
                  key={priority}
                  onClick={() => handlePriorityUpdate(priority)}
                  className={`${getPriorityColor(priority)} ${
                    complaint.priority === priority ? "bg-gray-100" : ""
                  }`}
                >
                  {getPriorityLabel(priority)}
                  {complaint.priority === priority && " (Hiện tại)"}
                </DropdownMenuItem>
              ))}
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Modal chi tiết khiếu nại */}
      {openModal && (
        <ComplaintDetailModal
          complaintSelect={complaint}
          isOpen={openModal}
          onClose={() => setOpenModal(false)}
        />
      )}
    </>
  );
};

export default ActionsDropdown;
