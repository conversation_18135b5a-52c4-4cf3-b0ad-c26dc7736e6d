import { z } from "zod"

export const additionalFieldSchema = z.object({
  name: z.string(),
  displayName: z.string(),
  type: z.enum(["text", "date"]),
  required: z.boolean().default(false)
})

export const bookingSchema = z
  .object({
    type: z.string().min(1, "<PERSON><PERSON> lòng chọn dịp"),
    recipient: z.enum(["someone_else", "myself"]),
    name: z.string().optional(),
    pronoun: z.string().min(1, "Chọn xưng hô"),
    request_details: z.string().min(1, "Nhập thông tin chi tiết"),
    additional: z.record(z.any()).optional() 
  });
export type BookingFormValues = z.infer<typeof bookingSchema>
