import { ColumnDef } from "@tanstack/react-table"
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header"
import { Button } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import Link from "next/link"

export interface TalentServiceDto {
  id: string
  serviceTitle: string
  serviceDescription: string
  price: number
  deliveryTime: string
  totalOrders?: number
  averageRating?: number
  numberOfReviews?: number
  isActive: boolean
}

export const talentServiceColumns: ColumnDef<TalentServiceDto>[] = [
  {
    accessorKey: "serviceTitle",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tên dịch vụ" />
    ),
    cell: ({ row }) => <div className="font-medium">{row.getValue("serviceTitle")}</div>,
  },
  {
    accessorKey: "serviceDescription",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mô tả" />
    ),
    cell: ({ row }) => (
      <div className="line-clamp-2 max-w-[250px] text-sm text-muted-foreground">
        {row.getValue("serviceDescription")}
      </div>
    ),
  },
  {
    accessorKey: "price",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Giá dịch vụ" />
    ),
    cell: ({ row }) => {
      const price = row.getValue("price") as number
      return <div>{price.toLocaleString("vi-VN")} ₫</div>
    },
  },
  {
    accessorKey: "deliveryTime",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Thời gian giao" />
    ),
    cell: ({ row }) => <div>{row.getValue("deliveryTime")}</div>,
  },
  {
    accessorKey: "totalOrders",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Số lượt đặt" />
    ),
    cell: ({ row }) => <div>{row.getValue("totalOrders") || 0}</div>,
  },
  {
    accessorKey: "averageRating",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Đánh giá TB" />
    ),
    cell: ({ row }) => {
      const rating = row.getValue("averageRating") as number | undefined
      return <div>{rating ? `${rating}/5 ★` : "Chưa có"}</div>
    },
  },
  {
    accessorKey: "numberOfReviews",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Lượt đánh giá" />
    ),
    cell: ({ row }) => <div>{row.getValue("numberOfReviews") || 0}</div>,
  },
  {
    accessorKey: "isActive",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Trạng thái" />
    ),
    cell: ({ row }) => {
      const active = row.getValue("isActive") as boolean
      return (
        <span
          className={`font-semibold ${
            active ? "text-green-600" : "text-gray-400 italic"
          }`}
        >
          {active ? "Đang hiển thị" : "Tạm ẩn"}
        </span>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const service = row.original
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Mở menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Hành động</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(service.serviceTitle)}
            >
              Sao chép tên dịch vụ
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Link href={`/doi-tac/san-pham/${service.id}`}> Xem chi tiết</Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
