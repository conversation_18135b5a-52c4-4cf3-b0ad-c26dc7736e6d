export interface TalentService {
  id: string
  serviceTitle: string
  serviceDescription: string
  price: number
  deliveryTime: string
  sampleVideos?: string[] // URL hoặc file name
  totalOrders?: number
  averageRating?: number // VD: 4.5
  numberOfReviews?: number
  isActive: boolean
  createdAt?: string
  updatedAt?: string
  tags?: string[]
}

export const getTalentServices = async (params : any): Promise<{ data: TalentService[]; totalItems: number }> => {
  // dummy data
  return {
    data: [
      {
        id: "1",
        serviceTitle: "Video Chúc Mừng Sinh Nhật",
        serviceDescription: "Gửi lời chúc mừng sinh nhật đặc biệt qua video.",
        price: 500000,
        deliveryTime: "3 ngày",
        sampleVideos: ["https://example.com/sample1.mp4", "https://example.com/sample2.mp4"],
        totalOrders: 120,
        averageRating: 4.8,
      numberOfReviews: 50,
      isActive: true,
      createdAt: "2023-01-01T00:00:00Z",
      updatedAt: "2023-10-01T00:00:00Z",
      tags: ["sinh nhật", "video cá nhân"]
    },
    {
      id: "2",
      serviceTitle: "Video Giới Thiệu Sản Phẩm",
      serviceDescription: "Tạo video giới thiệu sản phẩm chuyên nghiệp.",
      price: 1000000,
      deliveryTime: "5 ngày",
      sampleVideos: ["https://example.com/sample3.mp4"],
      totalOrders: 80,
      averageRating: 4.9,
      numberOfReviews: 30,
      isActive: true,
      createdAt: "2023-02-01T00:00:00Z",
      updatedAt: "2023-10-01T00:00:00Z",
      tags: ["giới thiệu sản phẩm", "video marketing"]
    }
  ],
  totalItems: 2 // Tổng số dịch vụ
  };
}