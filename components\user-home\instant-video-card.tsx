import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"

interface InstantVideoCardProps {
  name: string
  role: string
  price: string
  rating: number
  reviews: number
  imageUrl: string
  isOnline: boolean
}

export function InstantVideoCard({ name, role, price, rating, reviews, imageUrl, isOnline }: InstantVideoCardProps) {
  return (
    <Link href={`/talent/${name.toLowerCase().replace(/\s+/g, "-")}`} className="min-w-[190px] max-w-[150px]">
      <div className="relative rounded-lg overflow-hidden">
        <Image
          src={imageUrl || "/placeholder.svg"}
          alt={name}
          width={150}
          height={200}
          className="w-full aspect-[3/4] object-cover"
        />
      </div>
      <div className="mt-2">
        <h3 className="font-bold text-sm truncate">{name}</h3>
        <p className="text-xs text-gray-400 truncate">{role}</p>
        <div className="flex items-center gap-1 mt-1">
          <Star className="h-3 w-3 fill-yellow-500 stroke-none" />
          <span className="text-xs">{rating}</span>
          <span className="text-xs text-gray-400">({reviews})</span>
        </div>
        <p className="font-bold text-sm mt-1">{price}</p>
      </div>
    </Link>
  )
}
