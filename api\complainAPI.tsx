export interface ComplaintOverview {
  complaintId: string
  orderId: string
  user: {
    id: string
    name: string
    email: string
    avatarUrl?: string
  }
  talent: {
    id: string
    name: string
    avatarUrl?: string
  }
  serviceName: string
  createdAt: string // ISO date
  status: 'processing' | 'resolved' | 'refunded' | 'rejected'
  reasonExcerpt: string
}

export const getComplaintAdmin = async (params: any): Promise<{ complaints: ComplaintOverview[]; totalItems: number }> => {
  // Simulate fetching data from an API
  const complaints: ComplaintOverview[] = Array.from({ length: params.limit }, (_, index) => ({
    complaintId: `CMP${index + 1 + (params.page - 1) * params.limit}`,
    orderId: `ORD${index + 1 + (params.page - 1) * params.limit}`,
    user: {
      id: `USER${index + 1 + (params.page - 1) * params.limit}`,
      name: `User ${index + 1 + (params.page - 1) * params.limit}`,
      email: `user${index + 1 + (params.page - 1) * params.limit}@example.com`,
        avatarUrl: `https://example.com/user${index + 1 + (params.page - 1) * params.limit}.jpg`
    },
    talent: {
      id: `TALENT${index + 1 + (params.page - 1) * params.limit}`,
      name: `Talent ${index + 1 + (params.page - 1) * params.limit}`,
      avatarUrl: `https://example.com/talent${index + 1 + (params.page - 1) * params.limit}.jpg`
    },
    serviceName: `Service ${index + 1 + (params.page - 1) * params.limit}`,
    createdAt: new Date().toISOString(),
    status: ['processing', 'resolved', 'refunded', 'rejected'][index % 4] as 'processing' | 'resolved' | 'refunded' | 'rejected',
    reasonExcerpt: `Reason excerpt for complaint ${index + 1 + (params.page - 1) * params.limit}`
  }))
    return {
        complaints,
        totalItems: 100 // Simulated total items
    }
}
