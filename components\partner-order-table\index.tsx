import { useState, useEffect, useMemo } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '../ui/tabs'
import { Order } from '@/api/models/order/view-order'
import { getTalentOrders } from '@/api/orderAPI'
import { OrderStatusConsts, OrderStatusKey } from '@/lib/common/order-status-consts'
import { occasionList } from '../user-order/occasion'
import { getTalentOrderColumns } from './columns'

export default function TalentPaymentHistoryTable() {
  const [data, setData] = useState<Order[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderStatus, setOrderStatus] = useState('tat-ca');

  const partnerTableColumns = useMemo(() => {
    return getTalentOrderColumns();
  }, []);

  const fetchData = async () => {
    try {
      const data = await getTalentOrders();
      let orderData = data.data.map((order) => {
        return {
          ...order,
          status: OrderStatusConsts[order.status as OrderStatusKey]?.label || 'Chờ xử lý',
          type: occasionList.find(o => o.name === order.type)?.displayName || 'Không xác định',
          user: order.user,
          createdAt: order.createdAt ? new Date(order.createdAt).toLocaleDateString('vi-VN') : 'Chưa có',
          deliveryDate: order.deliveryDate ? new Date(order.deliveryDate).toLocaleDateString('vi-VN') : 'Chưa có',
        }
      });
      setData(orderData);
      setTotalItems(data.meta.total);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm])

  const toolbarComponent = (
    <div className="flex items-center justify-between">
       <Tabs defaultValue="tat-ca" value={orderStatus} onValueChange={setOrderStatus}>
        <TabsList className="flex flex-wrap gap-2">
          <TabsTrigger value="tat-ca">Tất cả</TabsTrigger>
          <TabsTrigger value="cho-xu-ly">Chờ xử lý</TabsTrigger>
          <TabsTrigger value="dang-xu-ly">Đang xử lý</TabsTrigger>
          <TabsTrigger value="da-gui">Đã gửi</TabsTrigger>
          <TabsTrigger value="hoan-thanh">Hoàn thành</TabsTrigger>
          <TabsTrigger value="khieu-nai">Khiếu nại</TabsTrigger>
        </TabsList>
      </Tabs>
      <div>
        <div className="flex flex-1 items-center space-x-2">
          <input
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8 w-[150px] lg:w-[250px] p-2 rounded-sm"
          />
        </div>
      </div>
    </div>
  )
  return (
      <div className='flex flex-col'>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Danh sách đơn hàng</h1>
        </div>
        <DataTable
          columns={partnerTableColumns}
          data={data}
          manualPagination={true}
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          onSearchChange={setSearchTerm}
          toolbarComponent={toolbarComponent}
        />
      </div>
  )
}