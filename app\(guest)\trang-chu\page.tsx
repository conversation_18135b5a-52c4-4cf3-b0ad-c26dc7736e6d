"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Search, Star, TrendingUp, Clock, Filter } from "lucide-react"

const talents = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON><PERSON>n <PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent1",
    category: "Ca sĩ",
    rating: 4.8,
    reviews: 120,
    price: 500000,
    responseTime: "24h",
    featured: true,
  },
  {
    id: "2",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent2",
    category: "Diễn viên",
    rating: 4.9,
    reviews: 85,
    price: 800000,
    responseTime: "12h",
    featured: true,
  },
  {
    id: "3",
    name: "Lê Văn C",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent3",
    category: "Influencer",
    rating: 4.7,
    reviews: 64,
    price: 400000,
    responseTime: "48h",
    featured: false,
  },
  {
    id: "4",
    name: "Phạm Thị D",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent4",
    category: "MC",
    rating: 4.6,
    reviews: 42,
    price: 600000,
    responseTime: "24h",
    featured: false,
  },
  {
    id: "5",
    name: "Hoàng Văn E",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent5",
    category: "Diễn viên",
    rating: 4.9,
    reviews: 110,
    price: 900000,
    responseTime: "6h",
    featured: true,
  },
  {
    id: "6",
    name: "Ngô Thị F",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent6",
    category: "Ca sĩ",
    rating: 4.5,
    reviews: 78,
    price: 550000,
    responseTime: "36h",
    featured: false,
  },
]

// Categories
const categories = ["Tất cả", "Ca sĩ", "Diễn viên", "Influencer", "MC", "Người mẫu", "Vận động viên"]

export default function UserHomePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("Tất cả")

  const filteredTalents = talents.filter((talent) => {
    const matchesSearch = talent.name.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "Tất cả" || talent.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const featuredTalents = talents.filter((talent) => talent.featured)

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Chào mừng đến với TalentVid</h1>
        <p className="text-muted-foreground">Đặt video cá nhân từ người nổi tiếng yêu thích của bạn</p>
      </div>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm talent..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex gap-2">
            <Filter className="h-4 w-4" />
            Lọc
          </Button>
          <Button variant="outline" className="flex gap-2">
            <TrendingUp className="h-4 w-4" />
            Sắp xếp
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="mb-4 overflow-auto">
          {categories.map((category) => (
            <TabsTrigger key={category} value={category} onClick={() => setSelectedCategory(category)}>
              {category}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {selectedCategory === "Tất cả" && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                Talent nổi bật
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {featuredTalents.map((talent) => (
                  <TalentCard key={talent.id} talent={talent} />
                ))}
              </div>
            </div>
          )}

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Tất cả Talent</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTalents.map((talent) => (
                <TalentCard key={talent.id} talent={talent} />
              ))}
            </div>
          </div>
        </TabsContent>

        {categories.slice(1).map((category) => (
          <TabsContent key={category} value={category}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTalents.map((talent) => (
                <TalentCard key={talent.id} talent={talent} />
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}

type Talent = {
  id: string
  name: string
  avatar: string
  category: string
  rating: number
  reviews: number
  price: number
  responseTime: string
  featured: boolean
}

function TalentCard({ talent }: { talent: Talent }) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        <div className="relative h-40 w-full bg-gradient-to-r from-primary/20 to-secondary/20">
          <div className="absolute bottom-0 left-0 right-0 p-4 flex items-end">
            <Avatar className="h-16 w-16 border-4 border-background">
              <AvatarImage src={talent.avatar || "/placeholder.svg"} alt={talent.name} />
              <AvatarFallback>{talent.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="ml-4">
              <CardTitle className="text-lg">{talent.name}</CardTitle>
              <CardDescription>{talent.category}</CardDescription>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <Star className="h-4 w-4 text-yellow-500 mr-1" />
            <span className="font-medium">{talent.rating}</span>
            <span className="text-muted-foreground ml-1">({talent.reviews})</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-muted-foreground mr-1" />
            <span className="text-sm text-muted-foreground">{talent.responseTime}</span>
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-lg font-bold">
            {new Intl.NumberFormat("vi-VN", {
              style: "currency",
              currency: "VND",
            }).format(talent.price)}
          </div>
          {talent.featured && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              Nổi bật
            </Badge>
          )}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button asChild className="w-full">
          <Link href={`/talent/${talent.id}`}>Xem chi tiết</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
