"use client"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Star, Clock, Calendar, MessageSquare, CheckCircle, MoreHorizontal, Play, Video, Share, MessageCircle, Heart, Briefcase } from "lucide-react"
import { getTalentBySlug } from "@/api/adminTalentAPI"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import { getErrorMessage } from "@/lib/utils"
import { User } from "@/api/models/user"
import { BookingForm } from "../user-order/booking-form"

type TalentInfoPageProps = {
  slug: string
}

export default function TalentInfoPage({ slug }: TalentInfoPageProps) {

  const [talent, setTalent] = useState<User | null>(null);
  const [isVideoDialogOpen, setIsVideoDialogOpen] = useState(false);
  const [videoToPlay, setVideoToPlay] = useState<any>(null);

  const getTalentInfor = async () => {
    try {
      const talentData = await getTalentBySlug(slug);
      setTalent(talentData);
    } catch (error) {
      toast.error(getErrorMessage(error));
    }
  }
  useEffect(() => {
    getTalentInfor();
  }, [slug]);

  const playVideo = (video: any) => {
    setVideoToPlay(video);
    setIsVideoDialogOpen(true);
  }

  return (
    <div className="space-y-6">
      <div className="min-h-screen">
      <div>
        <Dialog open={isVideoDialogOpen} onOpenChange={setIsVideoDialogOpen}>
          <DialogContent className="sm:max-w-3xl">
            <DialogHeader>
              <DialogTitle className="text-center">Xem video</DialogTitle>
              <DialogDescription className="text-center">
                {videoToPlay?.title || "Video từ " + talent?.name}
              </DialogDescription>
            </DialogHeader>
            <video src={videoToPlay?.videoLink} controls className="w-full rounded-md" />
          </DialogContent>
        </Dialog>
      </div>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
       
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="w-12 h-12">
              <AvatarImage src={talent?.avatar || "/placeholder.svg?height=48&width=48"} alt={talent?.name} />
              <AvatarFallback>JB</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="font-semibold text-lg">{talent?.name}</h1>
              <p className=" text-sm">{talent?.job}</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-2">
          {talent?.videos?.map((video, i) => (
            <Card key={i} className="bg-gray-800 border-gray-700 relative aspect-square" onClick={() => playVideo(video)}>
              <CardContent className="p-0 h-full">
                <div className="relative h-full bg-gradient-to-br from-amber-600 to-orange-700 rounded-lg flex items-center justify-center">
                  <img src={video.thumbnailLink} alt={video.title} className="absolute z-0 w-full h-full object-cover rounded-lg" />
                  <Play className="w-6 h-6 text-white absolute z-10" />
                  <div className="absolute bottom-1 left-1 text-xs bg-black/50 px-1 rounded">{video.title}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex justify-between text-sm">
          <div>
            <div className="">Giá</div>
            <div className="font-semibold">{talent?.price} </div>
          </div>
          <div className="text-center">
            <div className="">Có sẵn cho</div>
            <div className="font-semibold flex items-center gap-1">
              <Clock className="w-4 h-4" />
              24 giờ giao hàng
            </div>
          </div>
          <div className="text-right">
            <div className="">Đánh giá</div>
            <div className="font-semibold flex items-center gap-1">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              4.97 (12003)
            </div>
          </div>
        </div>

        <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-full" onClick={() => setIsVideoDialogOpen(true)}>
          <Video className="w-4 h-4 mr-2" />
          Đặt video từ {talent?.name}
        </Button>

        <div>
          <h3 className="font-semibold mb-3">LÝ DO ĐỂ ĐẶT VIDEO</h3>
          <div className="flex flex-wrap gap-2">
            {talent?.tags?.map((reason, i) => (
              <Badge key={i} variant="secondary">
                {/* <span className="mr-1">{reason.icon}</span> */}
                {reason}
              </Badge>
            ))}
          </div>
        </div>

        <div className="text-sm  leading-relaxed">
          <p>
            {talent?.description}
          </p>
        </div>

        <div className="text-sm  space-y-1">
          <div>
            • Thời gian trung bình của video <span className="text-white">00:42</span>
          </div>
          <div>
            • Video hoàn thành gần nhất: <span className="text-white">hôm qua lúc 11:37 PM</span>
          </div>
        </div>

        {/* What to Expect */}
        <div>
          <h3 className="font-semibold mb-3">NHỮNG GÌ CÓ THỂ MONG ĐỢI</h3>
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <CheckCircle className="w-4 h-4 " />
              <span>Viết một bộ hướng dẫn ngắn</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Video className="w-4 h-4 " />
              <span>Nhận video của bạn</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Share className="w-4 h-4 " />
              <span>Chia sẻ với bất cứ ai</span>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="flex items-center justify-between">
          <span className="text-sm ">Đảm bảo hoàn tiền</span>
          <div className="flex gap-2">
            {["MASTERCARD", "VISA"].map((card) => (
              <div key={card} className="w-16 h-5  rounded text-xs flex items-center justify-center">
                {card}
              </div>
            ))}
          </div>
        </div>

        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-4 text-center">CÁC CÁCH KẾT NỐI KHÁC</h3>
            <div className="space-y-3">
              <Button variant="outline" className="w-full">
                <MessageCircle className="w-4 h-4 mr-2" />
                Gửi tin nhắn 
              </Button>
              <Button variant="outline" className="w-full">
                <Heart className="w-4 h-4 mr-2" />
                Theo dõi để nhận cập nhật
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-3">THÔNG TIN THÊM VỀ {talent?.name}</h3>
            <p className="text-sm  mb-4">
              {talent?.description}
            </p>
            <div className="space-y-2 text-sm">
              <div>
                <span className="">Tuổi:</span> 37
              </div>
              <div>
                <span className="">Ngày sinh:</span> 08/14
              </div>
              <div>
                <span className="">Cung hoàng đạo:</span> Leo
              </div>
              <div>
                <span className="">Tham gia :</span> {new Date(talent?.createdAt || "").toLocaleDateString("vi-VN")}
              </div>
            </div>
            <div className="flex flex-wrap gap-2 mt-4">
              {talent?.categories?.map((category) => (
                <Badge key={category.id} variant="secondary">
                  {category.name}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-4">
              <h3 className="font-semibold">Đánh giá gần đây</h3>
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm">4.97 (12003)</span>
              </div>
            </div>

            <div className="space-y-4">
              {[
                {
                  name: "Anonymous",
                  date: "Sun Jun 22 2025",
                  rating: 5,
                  text: "Great video message for my U16 football team and of what to do. The lads and parents loved it...",
                },
                {
                  name: "Anonymous",
                  date: "Sun Jun 22 2025",
                  rating: 5,
                  text: "It was brilliant and was really quick with the response",
                },
                {
                  name: "Anonymous",
                  date: "Sun Jun 22 2025",
                  rating: 5,
                  text: "Ordered for my sons 18th birthday. GCSE results day. Absolutely brilliant...",
                },
              ].map((review, i) => (
                <div key={i} className="border-b border-gray-800 pb-4 last:border-b-0">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-sm">{review.name}</span>
                    <div className="flex">
                      {[...Array(review.rating)].map((_, j) => (
                        <Star key={j} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                  <div className="text-xs  mb-2">{review.date}</div>
                  <p className="text-sm ">{review.text}</p>
                  <Button variant="link" className=" p-0 h-auto text-sm">
                    Đọc thêm
                  </Button>
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              className="w-full mt-4 bg-transparent border-gray-600 "
            >
              Hiển thị tất cả đánh giá (12003)
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-amber-600 to-orange-600 border-0">
          <CardContent className="p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-black/20 rounded-lg flex items-center justify-center">
                <Briefcase className="w-5 h-5 text-white" />
              </div>
              <span className="font-semibold">Promote your business with James Buckley</span>
            </div>
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/20">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
    <Dialog open={isVideoDialogOpen} onOpenChange={setIsVideoDialogOpen}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-center">Đặt sản phẩm</DialogTitle>
        </DialogHeader>
        <BookingForm talentId={talent?.id} />
      </DialogContent>
    </Dialog>
    </div>
  )
}
