import { ColumnDef } from "@tanstack/react-table"
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header"
import { Button } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Category } from "@/api/models/category"
import { deleteCategory } from "@/api/categoryAPI"

type Props = {
  deleteFunction?: (id: number) => Promise<void>;
  viewFunction?: (id: number) => void;
  editFunction?: (id: number) => void;
}

export const getAdminCategoryColumns = (props: Props): ColumnDef<Category>[] => {

  return [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="ID" />
      ),
      cell: ({ row }) => (
        <div className="font-mono text-xs">{row.getValue("id")}</div>
      ),
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tên danh mục" />
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "slug",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Slug" />
      ),
      cell: ({ row }) => {
        const slug = row.getValue("slug") as string | null
        return <div>{slug || "—"}</div>
      },
    },
    {
      accessorKey: "thumbnail",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Ảnh" />
      ),
      cell: ({ row }) => {
        const thumbnail = row.getValue("thumbnail") as string | null
        return thumbnail ? (
          <img
            src={thumbnail}
            alt="thumbnail"
            className="w-10 h-10 object-cover rounded"
          />
        ) : (
          <div className="text-gray-400">Không có</div>
        )
      },
    },
    {
      accessorKey: "parent",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Danh mục cha" />
      ),
      cell: ({ row }) => {
        const parent = row.getValue("parent") as Category | null
        return <div>{parent?.name || "—"}</div>
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Ngày tạo" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"))
        return <div>{date.toLocaleDateString("vi-VN")}</div>
      },
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Ngày cập nhật" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("updatedAt"))
        return <div>{date.toLocaleDateString("vi-VN")}</div>
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const category = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Mở menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Hành động</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(String(category.id))}
              >
                Sao chép ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => props.viewFunction?.(category.id)}>
                Xem chi tiết
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => props.editFunction?.(category.id)}>
                Chỉnh sửa
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => props.deleteFunction?.(category.id)}>
                Xoá
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
}
