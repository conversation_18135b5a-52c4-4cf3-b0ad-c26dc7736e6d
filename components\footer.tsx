import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowRight, Instagram, Facebook, Mail, Globe } from "lucide-react"
import Link from "next/link"

export default function Footer() {
    return (
        <footer className="py-12 px-6">
            <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-8">
                    {/* Newsletter Section */}
                    <div className="lg:col-span-5 space-y-6">
                        <div>
                            <h3 className="text-xl font-semibold mb-3">Join our mailing list</h3>
                            <p className="text-gray-400 mb-6">Be the first to know about the newest stars and best deals on Cameo</p>

                            <div className="flex gap-2 mb-3">
                                <Input
                                    type="email"
                                    placeholder="Your email"
                                    className=" flex-1"
                                />
                                <Button size="icon" className="">
                                    <ArrowRight className="h-4 w-4" />
                                </Button>
                            </div>

                            <p className="text-xs text-gray-500 mb-6">
                                Emails subject to{" "}
                                <Link href="#" className="underline">
                                    privacy policy
                                </Link>
                            </p>
                        </div>

                        <div className="flex gap-3">
                            <Button className="b">Join as talent</Button>
                            <Button variant="outline" className="">
                                Become a partner
                            </Button>
                        </div>
                    </div>

                    {/* Navigation Links */}
                    <div className="lg:col-span-4 grid grid-cols-1 sm:grid-cols-3 gap-8">
                        {/* Company */}
                        <div>
                            <h4 className="font-semibold mb-4">Company</h4>
                            <ul className="space-y-3 text-gray-400">
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        About
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Team
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Jobs
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Blog
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Press
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        {/* Support */}
                        <div>
                            <h4 className="font-semibold mb-4">Support</h4>
                            <ul className="space-y-3 text-gray-400">
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Help
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Accessibility
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Refunds & Returns
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        {/* Shop */}
                        <div>
                            <h4 className="font-semibold mb-4">Shop</h4>
                            <ul className="space-y-3 text-gray-400">
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        Gift Cards
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        For business
                                    </Link>
                                </li>
                                <li>
                                    <Link href="#" className="hover:text-white transition-colors">
                                        For kids
                                    </Link>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="lg:col-span-3 space-y-6">
                        <div className="flex flex-col gap-3">
                            <Link href="#" className="block">
                                <div className="rounded-lg px-4 py-2 flex items-center gap-3 hov">
                                    <div>
                                        <div className="text-xs">Download on the</div>
                                        <div className="font-semibold">App Store</div>
                                    </div>
                                </div>
                            </Link>

                            <Link href="#" className="block">
                                <div className="rounded-lg px-4 py-2 flex items-center gap-3 ">
                                    <div>
                                        <div className="text-xs">GET IT ON</div>
                                        <div className="font-semibold">Google Play</div>
                                    </div>
                                </div>
                            </Link>
                        </div>

                        {/* Social Media Icons */}
                        <div className="flex gap-3">
                            <Link
                                href="#"
                                className="w-10 h-10 rounded-full flex items-center justify-center "
                            >
                                <Instagram className="h-5 w-5" />
                            </Link>
                            <Link
                                href="#"
                                className="w-10 h-10 rounded-full flex items-center justify-center "
                            >
                                <div className="text-sm font-bold">T</div>
                            </Link>
                            <Link
                                href="#"
                                className="w-10 h-10 rounded-full flex items-center justify-center "
                            >
                                <div className="text-sm font-bold">X</div>
                            </Link>
                            <Link
                                href="#"
                                className="w-10 h-10 rounded-full flex items-center justify-center "
                            >
                                <Facebook className="h-5 w-5" />
                            </Link>
                            <Link
                                href="#"
                                className="w-10 h-10 rounded-full flex items-center justify-center "
                            >
                                <Mail className="h-5 w-5" />
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Bottom Section */}
                <div className="border-t border-gray-800 pt-6 flex flex-col sm:flex-row justify-between items-center gap-4 text-sm text-gray-400">
                    <div className="flex flex-wrap gap-4">
                        <span>© 2017-2025, Baron App, Inc. dba Cameo</span>
                        <Link href="#" className="hover:text-white transition-colors flex items-center gap-1">
                            Your Privacy Choices
                        </Link>
                        <Link href="#" className="hover:text-white transition-colors">
                            Terms
                        </Link>
                        <Link href="#" className="hover:text-white transition-colors">
                            Privacy
                        </Link>
                    </div>

                    <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        <span>EN | United States of America | đ VND</span>
                    </div>
                </div>
            </div>
        </footer>
    )
}
