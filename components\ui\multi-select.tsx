import { useState } from "react"
import { <PERSON> } from "react-hook-form"
import { <PERSON>over, <PERSON>overContent, PopoverTrigger } from "./popover"
import { Button } from "./button"
import { cn } from "@/lib/utils"
import { Check, ChevronsUpDown } from "lucide-react"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "./command"
import { Badge } from "./badge"

type Option = {
  label: string;
  value: string;
}

interface MultiSelectProps {
  name: string;
  control: any;
  options: Option[];
  placeholder?: string;
  rules?: any;
}

export function MultiSelect({
  name,
  control,
  options,
  placeholder = "Chọn...",
  rules,
}: MultiSelectProps) {
  const [open, setOpen] = useState(false);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => {
        const selectedValues = field.value || []

        return (
          <>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  className={cn(
                    "w-full justify-between",
                    selectedValues.length === 0 && "text-muted-foreground"
                  )}
                >
                  <div className="flex flex-wrap gap-2 overflow-hidden max-w-full max-h-[100%]">
                    {selectedValues.map((item: any) => (
                      <Badge
                        key={item.value}
                        variant="secondary"
                        className="cursor-default"
                      >
                        {item.label}
                      </Badge>
                    ))}
                  </div>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-0">
                <Command>
                  <CommandInput placeholder="Tìm..." />
                  <CommandEmpty>Không tìm thấy.</CommandEmpty>
                  <CommandGroup>
                    {options.map((option) => {
                      const isSelected = selectedValues.some(
                        (item: any) => item.value === option.value
                      )

                      return (
                        <CommandItem
                          key={option.value}
                          onSelect={() => {
                            const newValue = isSelected
                              ? selectedValues.filter(
                                (item: any) => item.value !== option.value
                              )
                              : [...selectedValues, option]
                            field.onChange(newValue)
                          }}
                        >
                          <div
                            className={cn(
                              "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                              isSelected
                                ? "bg-primary text-primary-foreground"
                                : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <Check className="h-4 w-4" />
                          </div>
                          {option.label}
                        </CommandItem>
                      )
                    })}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>

            {fieldState.error && (
              <p className="text-sm text-red-500">
                {fieldState.error.message}
              </p>
            )}
          </>
        )
      }}
    />
  );
}
