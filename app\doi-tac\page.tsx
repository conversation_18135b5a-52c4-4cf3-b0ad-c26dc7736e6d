"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DollarSign, Video, Clock, Star, Calendar, MessageSquare, CheckCircle } from "lucide-react"
import Link from "next/link"

// Mock data for partner dashboard
const stats = {
  totalEarnings: 15750000,
  pendingOrders: 3,
  completedOrders: 47,
  averageRating: 4.8,
  responseTime: "12h",
  thisMonthEarnings: 3250000,
}

const recentOrders = [
  {
    id: "ord-101",
    customer: "Nguyễn Văn A",
    service: "Video chúc mừng sinh nhật",
    price: 500000,
    status: "moi",
    createdAt: "2023-07-15",
    deliveryDate: "2023-07-18",
  },
  {
    id: "ord-102",
    customer: "Trần Thị B",
    service: "Video động viên",
    price: 600000,
    status: "dang-thuc-hien",
    createdAt: "2023-07-14",
    deliveryDate: "2023-07-17",
  },
  {
    id: "ord-103",
    customer: "Lê Văn C",
    service: "Video quảng cáo",
    price: 1500000,
    status: "da-gui-video",
    createdAt: "2023-07-13",
    deliveryDate: "2023-07-16",
  },
]

const upcomingDeadlines = [
  {
    id: "ord-102",
    customer: "Trần Thị B",
    service: "Video động viên",
    deadline: "2023-07-17",
    daysLeft: 2,
  },
  {
    id: "ord-104",
    customer: "Phạm Thị D",
    service: "Video chúc mừng",
    deadline: "2023-07-19",
    daysLeft: 4,
  },
]

const statusMap = {
  moi: {
    label: "Mới",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  },
  "dang-thuc-hien": {
    label: "Đang thực hiện",
    color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  },
  "da-gui-video": {
    label: "Đã gửi video",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  },
}

export default function PartnerDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Dashboard Đối tác</h1>
        <p className="text-muted-foreground">Quản lý đơn hàng và theo dõi thu nhập của bạn</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng thu nhập</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
              }).format(stats.totalEarnings)}
            </div>
            <p className="text-xs text-muted-foreground">
              +
              {new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
              }).format(stats.thisMonthEarnings)}{" "}
              tháng này
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đơn chờ xử lý</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingOrders}</div>
            <p className="text-xs text-muted-foreground">Cần xử lý sớm</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đơn hoàn thành</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedOrders}</div>
            <p className="text-xs text-muted-foreground">Tổng cộng</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đánh giá trung bình</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageRating}</div>
            <p className="text-xs text-muted-foreground">Phản hồi trong {stats.responseTime}</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Đơn hàng gần đây</CardTitle>
              <CardDescription>Quản lý và theo dõi đơn hàng của bạn</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">#{order.id}</h3>
                        <Badge className={statusMap[order.status].color}>{statusMap[order.status].label}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {order.customer} - {order.service}
                      </p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-sm text-muted-foreground">
                          <Calendar className="inline mr-1 h-3 w-3" />
                          Hạn: {order.deliveryDate}
                        </span>
                        <span className="font-medium">
                          {new Intl.NumberFormat("vi-VN", {
                            style: "currency",
                            currency: "VND",
                          }).format(order.price)}
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Chat
                      </Button>
                      <Button size="sm" asChild>
                        <Link href={`/doi-tac/don-hang/${order.id}`}>Chi tiết</Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button className="w-full" variant="outline" asChild>
                  <Link href="/doi-tac/don-hang">Xem tất cả đơn hàng</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Deadline sắp tới</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingDeadlines.map((item) => (
                  <div key={item.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">#{item.id}</p>
                      <p className="text-sm text-muted-foreground">{item.customer}</p>
                      <p className="text-xs text-muted-foreground">{item.service}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant={item.daysLeft <= 2 ? "destructive" : "secondary"}>{item.daysLeft} ngày</Badge>
                      <p className="text-xs text-muted-foreground mt-1">{item.deadline}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Hành động nhanh</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" asChild>
                <Link href="/doi-tac/don-hang">
                  <Video className="mr-2 h-4 w-4" />
                  Xem đơn hàng mới
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/doi-tac/ho-so">
                  <Star className="mr-2 h-4 w-4" />
                  Cập nhật hồ sơ
                </Link>
              </Button>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/doi-tac/thanh-toan">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Xem thu nhập
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Thống kê tháng này</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Đơn hoàn thành</span>
                  <span className="font-medium">12</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Tỷ lệ hoàn thành</span>
                  <span className="font-medium">95%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Đánh giá trung bình</span>
                  <span className="font-medium">4.8/5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Thu nhập</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(stats.thisMonthEarnings)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
