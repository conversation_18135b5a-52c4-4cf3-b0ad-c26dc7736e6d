import api from "./axios";
import { CreateOrderRequest } from "./models/order/create-order";
import { Order } from "./models/order/view-order";
import { Paginator } from "./models/paginator";

export async function createOrder(data: CreateOrderRequest): Promise<Order> {
  const response = await api.post<Order>("/orders", data);
  return response.data;
}

export async function getUserOrders(): Promise<Paginator<Order>> {
  const response = await api.get<Paginator<Order>>("/orders/me");
  return response.data;
}

export async function cancelOrder(orderId: number): Promise<Order> {
  const response = await api.patch<Order>(`/orders/${orderId}/cancel`);
  return response.data;
}

export async function getOrderById(orderId: number): Promise<Order> {
  const response = await api.get<Order>(`/orders/${orderId}`);
  return response.data;
}

export async function acceptOrder(orderId: number): Promise<Order> {
  const response = await api.patch<Order>(`/orders/${orderId}/accept`);
  return response.data;
}

export async function rejectOrder(orderId: number): Promise<Order> {
  const response = await api.patch<Order>(`/orders/${orderId}/reject`);
  return response.data;
}

export async function payOrder(orderId: number): Promise<string> {
  const response = await api.get<string>(`/payment/payment-link/${orderId}`);
  return response.data;
}

export async function getTalentOrders(): Promise<Paginator<Order>> {
  const response = await api.get<Paginator<Order>>(`/orders/talent/me`);
  return response.data;
}

export function updateOrderVideo(orderId: number, videoLink: string): Promise<Order> {
  return api.patch<Order>(`/orders/${orderId}/video-link`, { videoLink })
    .then(response => response.data);
}

