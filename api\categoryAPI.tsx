import api from "./axios";
import { Category } from "./models/category";

export const getAllCategories = async (): Promise<Category[]> => {
  const response = await api.get("/categories");
  return response.data as Category[];
}

export const createCategory = async (category: Category): Promise<Category> => {
  const response = await api.post("/categories", category);
  return response.data as Category;
}

export const updateCategory = async (category: Category): Promise<Category> => {
  const response = await api.patch(`/categories/${category.id}`, category);
  return response.data as Category;
}

export const deleteCategory = async (id: number): Promise<void> => {
  await api.delete(`/categories/${id}`);
}
