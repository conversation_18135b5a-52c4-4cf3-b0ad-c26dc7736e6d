import { Clock, Star } from "lucide-react"
import { getTop10Talents } from "@/api/adminTalentAPI"
import Image from "next/image"
import Link from "next/link"

export default async function InstantVideoSection() {
  const talents = await getTop10Talents()

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <div className="bg-gradient-to-r from-green-900 to-green-800 rounded-lg p-6">
          <div className="flex justify-between items-center mb-2">
            <div>
              <h2 className="text-xl font-bold text-white">Instant Cameo videos</h2>
              <p className="text-sm flex items-center gap-1 text-white/80">
                <Clock className="h-4 w-4" /> 10 min delivery
              </p>
            </div>
          </div>

          <div className="flex gap-4 pb-4 mt-6 overflow-x-auto scrollbar-hide">
            {talents.slice(0, 6).map((talent: any) => {
              const {
                name,
                nick_name,
                job: role,
                avatar: imageUrl,
                price,
              } = talent
              return (
                <Link
                  key={talent.id}
                  href={`/talent/${nick_name}`}
                  className="min-w-[150px] max-w-[150px] text-white"
                >
                  <div className="relative rounded-lg overflow-hidden">
                    <Image
                      src={imageUrl || "/placeholder.svg"}
                      alt={name}
                      width={150}
                      height={200}
                      className="w-full aspect-[3/4] object-cover"
                    />
                  </div>
                  <div className="mt-2">
                    <h3 className="font-bold text-sm truncate">{name}</h3>
                    <p className="text-xs text-white/70 truncate">{role}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="h-3 w-3 fill-yellow-500 stroke-none" />
                      <span className="text-xs">4.9</span>
                      <span className="text-xs text-white/50">(100)</span>
                    </div>
                    <p className="font-bold text-sm mt-1">${(price / 100).toFixed(0)}+</p>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
