import api from "./axios";
import { Paginator } from "./models/paginator";

export enum ComplaintStatus {
  PENDING = "pending",
  INVESTIGATING = "investigating",
  RESOLVED = "resolved",
  REJECTED = "rejected",
}

export enum ComplaintPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

export enum ComplaintType {
  VIDEO_QUALITY = "video_quality",
  DELIVERY_TIME = "delivery_time",
  CONTENT_ISSUE = "content_issue",
  PAYMENT_ISSUE = "payment_issue",
  OTHER = "other",
}

export interface ComplaintEvidenceImage {
  url: string;
  description?: string;
}

export interface Complaint {
  id: number;
  title: string;
  description: string;
  complaintType: ComplaintType;
  priority: ComplaintPriority;
  status: ComplaintStatus;
  resolution?: string;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;

  // User Info
  userId: number;
  resolvedById?: number;

  // Optional relations
  orderId?: number;
  videoId?: number;

  // Evidence: Mảng ảnh minh chứng
  evidence?: ComplaintEvidenceImage[];

  user?: any;
}

export interface ComplaintForm {
  id: number;
  title: string;
  description: string;
  complaintType: ComplaintType;
  priority: ComplaintPriority;
  status: ComplaintStatus;
  resolution?: string;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;

  // User Info
  userId: number;
  resolvedById?: number;

  // Optional relations
  orderId?: number;
  videoId?: number;

  // Evidence: Mảng ảnh minh chứng
  evidence?: ComplaintEvidenceImage[];
}

// Lấy danh sách khiếu nại có phân trang
export const getComplaints = async ({ queryKey }: { queryKey: any }) => {
  const [_, query] = queryKey;

  console.log("query", query);

  const response = await api.get<Paginator<Complaint>>("/complaints", {
    params: query,
  });
  return response.data;
};

// Lấy toàn bộ khiếu nại của người dùng hiện tại
export const getMyComplaints = async (): Promise<Complaint[]> => {
  const response = await api.get<Complaint[]>("/complaints/my-complaints");
  return response.data;
};

// Lấy chi tiết 1 khiếu nại theo ID
export const getComplaintById = async ({
  queryKey,
}: {
  queryKey: any;
}): Promise<Complaint> => {
  const [_, query] = queryKey;
  const response = await api.get<Complaint>(`/complaints/${query.id}`);
  return response.data;
};

// Tạo khiếu nại mới
export const createComplaint = async (
  data: Partial<Complaint>
): Promise<ComplaintForm> => {
  const response = await api.post<Complaint>("/complaints", data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

// Cập nhật khiếu nại
export const updateComplaint = async (
  id: number,
  data: Partial<Complaint>
): Promise<Complaint> => {
  const response = await api.patch<Complaint>(`/complaints/${id}`, data);
  return response.data;
};

// Gửi hình ảnh minh chứng (form-data)
export const uploadEvidenceImage = async (
  id: number,
  image: File,
  description?: string
): Promise<Complaint> => {
  const formData = new FormData();
  formData.append("image", image);
  if (description) formData.append("description", description);

  const response = await api.post<Complaint>(
    `/complaints/${id}/evidence/upload`,
    formData,
    { headers: { "Content-Type": "multipart/form-data" } }
  );
  return response.data;
};

// Thêm danh sách ảnh (dạng URL)
export const addEvidenceImages = async (
  id: number,
  images: Array<{ url: string; description?: string }>
): Promise<Complaint> => {
  const response = await api.post<Complaint>(
    `/complaints/${id}/evidence/images`,
    { images }
  );
  return response.data;
};

// Xóa ảnh theo index
export const deleteEvidenceImage = async (
  complaintId: number,
  imageIndex: number
): Promise<Complaint> => {
  const response = await api.delete<Complaint>(
    `/complaints/${complaintId}/evidence/images/${imageIndex}`
  );
  return response.data;
};

// Giải quyết khiếu nại (admin)
export const resolveComplaint = async (
  id: number,
  data: { status: ComplaintStatus }
): Promise<Complaint> => {
  const response = await api.patch<Complaint>(
    `/complaints/${id}/resolve`,
    data
  );
  return response.data;
};

export const priorityComplaint = async (
  id: number,
  data: { priority: ComplaintPriority }
): Promise<Complaint> => {
  const response = await api.patch<Complaint>(
    `/complaints/${id}/priority`,
    data
  );
  return response.data;
};

// Xóa khiếu nại
export const deleteComplaint = async (id: number): Promise<void> => {
  await api.delete(`/complaints/${id}`);
};
