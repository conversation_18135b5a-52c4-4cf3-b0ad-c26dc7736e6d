export interface TalentPaymentHistoryDto {
    orderId: string
    customerName: string
    serviceName: string
    orderDate: string // ISO date string
    completionDate?: string // optional
    servicePrice: number // Tổng giá người dùng thanh toán
    percentageReceived: number // phần trăm talent đ<PERSON><PERSON><PERSON> nh<PERSON>n, ví dụ 80 => 80%
    amountReceived?: number // số tiền thực nhận = servicePrice * percentageReceived / 100
    paymentStatus: "pending" | "processing" | "paid" | "failed"
    paymentDate?: string // optional
}

export const getTalentPaymentHistory = async (
    params: {
        page: number
        limit: number
        search?: string
    }
): Promise<{
    totalItems: number
    paymentHistory: TalentPaymentHistoryDto[]
}> => {
    // create dummpy data 
    const dummyData: TalentPaymentHistoryDto[] = Array.from({ length: params.limit }, (_, index) => ({
        orderId: `ORD-${params.page}-${index + 1}`,
        customerName: `Customer ${index + 1}`,
        serviceName: `Service ${index + 1}`,
        orderDate: new Date().toISOString(),
        servicePrice: Math.floor(Math.random() * 1000) + 100,
        percentageReceived: 80,
        amountReceived: Math.floor(Math.random() * 800) + 100,
        paymentStatus: ["pending", "processing", "paid", "failed"][Math.floor(Math.random() * 4)] as "pending" | "processing" | "paid" | "failed",
        paymentDate: new Date().toISOString(),
    }));
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                totalItems: 100, // giả sử có tổng cộng 100 mục
                paymentHistory: dummyData,
            });
        }, 1000); // giả lập delay 1 giây
    });
}
