"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CalendarIcon, User, Phone, Mail, Clock, Image } from "lucide-react";
import { Complaint, getComplaintById } from "@/api/complaintsAPI";
import { ComplaintStatus, ComplaintPriority, ComplaintType } from "./index";
import { useQuery } from "@tanstack/react-query";

interface ComplaintDetailModalProps {
  complaintSelect: any | null;
  isOpen: boolean;
  onClose: () => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case ComplaintStatus.PENDING:
      return "bg-yellow-100 text-yellow-800";
    case ComplaintStatus.INVESTIGATING:
      return "bg-blue-100 text-blue-800";
    case ComplaintStatus.RESOLVED:
      return "bg-green-100 text-green-800";
    case ComplaintStatus.REJECTED:
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case ComplaintPriority.LOW:
      return "bg-gray-100 text-gray-800";
    case ComplaintPriority.MEDIUM:
      return "bg-yellow-100 text-yellow-800";
    case ComplaintPriority.HIGH:
      return "bg-orange-100 text-orange-800";
    case ComplaintPriority.URGENT:
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getTypeLabel = (type: string) => {
  const typeLabels = {
    [ComplaintType.VIDEO_QUALITY]: "Chất lượng video",
    [ComplaintType.DELIVERY_TIME]: "Thời gian giao hàng",
    [ComplaintType.CONTENT_ISSUE]: "Vấn đề nội dung",
    [ComplaintType.PAYMENT_ISSUE]: "Vấn đề thanh toán",
    [ComplaintType.OTHER]: "Khác",
  };
  return typeLabels[type as keyof typeof typeLabels] || type;
};

const getStatusLabel = (status: string) => {
  const statusLabels = {
    [ComplaintStatus.PENDING]: "Chờ xử lý",
    [ComplaintStatus.INVESTIGATING]: "Đang điều tra",
    [ComplaintStatus.RESOLVED]: "Đã giải quyết",
    [ComplaintStatus.REJECTED]: "Đã từ chối",
  };
  return statusLabels[status as keyof typeof statusLabels] || status;
};

const getPriorityLabel = (priority: string) => {
  const priorityLabels = {
    [ComplaintPriority.LOW]: "Thấp",
    [ComplaintPriority.MEDIUM]: "Trung bình",
    [ComplaintPriority.HIGH]: "Cao",
    [ComplaintPriority.URGENT]: "Khẩn cấp",
  };
  return priorityLabels[priority as keyof typeof priorityLabels] || priority;
};

export default function ComplaintDetailModal({
  complaintSelect,
  isOpen,
  onClose,
}: ComplaintDetailModalProps) {
  const { data: complaint }: any = useQuery({
    queryKey: ["detail", { id: complaintSelect?.id }],
    queryFn: getComplaintById,
    enabled: !!complaintSelect?.id,
  });
  if (!complaint) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Chi tiết khiếu nại #{complaint.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-medium mb-2">{complaint.title}</h3>
              <div className="flex gap-2 mb-3">
                <Badge className={getStatusColor(complaint.status)}>
                  {getStatusLabel(complaint.status)}
                </Badge>
                <Badge className={getPriorityColor(complaint.priority)}>
                  {getPriorityLabel(complaint.priority)}
                </Badge>
                <Badge variant="outline">
                  {getTypeLabel(complaint.complaintType)}
                </Badge>
              </div>
            </div>
            <div className="text-right text-sm text-gray-600">
              <div className="flex justify-end items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                <span>
                  {new Date(complaint.createdAt).toLocaleDateString("vi-VN")}
                </span>
              </div>
              {complaint.resolvedAt && (
                <div className="flex justify-end items-center gap-2 mt-1">
                  <Clock className="h-4 w-4" />
                  <span>
                    Giải quyết:{" "}
                    {new Date(complaint.resolvedAt).toLocaleDateString("vi-VN")}
                  </span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* User info */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center gap-2">
              <User className="h-4 w-4" /> Thông tin người khiếu nại
            </h4>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center gap-3 mb-3">
                {complaint.user?.avatar && (
                  <img
                    src={complaint.user.avatar}
                    alt="Avatar"
                    className="w-12 h-12 rounded-full"
                  />
                )}
                <div>
                  <p className="font-medium">{complaint.user?.name}</p>
                  <p className="text-sm text-gray-600">
                    {complaint.user?.nick_name}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{complaint.user?.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{complaint.user?.phone}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Evidence images */}
          {complaint.evidence?.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="text-md font-medium mb-3 flex items-center gap-2">
                  <Image className="h-4 w-4" /> Bằng chứng hình ảnh
                </h4>
                <div className="bg-orange-50 p-4 rounded-lg grid grid-cols-1 md:grid-cols-2 gap-4">
                  {complaint.evidence.map((item: any, index: number) => (
                    <div key={index} className="relative group">
                      <img
                        src={item.url}
                        alt={`Evidence ${index + 1}`}
                        className="w-full h-48 object-cover rounded-lg border hover:brightness-105 cursor-pointer"
                        onClick={() => window.open(item.url, "_blank")}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition rounded-lg flex items-center justify-center">
                        <Button
                          variant="secondary"
                          size="sm"
                          className="bg-white text-gray-800 hover:bg-gray-100 opacity-0 group-hover:opacity-100"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(item.url, "_blank");
                          }}
                        >
                          Xem toàn màn hình
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Order Info */}
          {complaint.order && (
            <>
              <Separator />
              <div>
                <h4 className="text-md font-medium mb-3">Thông tin đơn hàng</h4>
                <div className="bg-blue-50 p-4 rounded-lg grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p>
                      <span className="font-medium">Mã đơn hàng:</span> #
                      {complaint.order.id}
                    </p>
                    <p>
                      <span className="font-medium">Loại video:</span>{" "}
                      {complaint.order.type}
                    </p>
                    <p>
                      <span className="font-medium">Giá:</span>{" "}
                      {complaint.order.price?.toLocaleString("vi-VN")} VNĐ
                    </p>
                  </div>
                  <div>
                    <p>
                      <span className="font-medium">Trạng thái:</span>{" "}
                      {complaint.order.status}
                    </p>
                    <p>
                      <span className="font-medium">Thanh toán:</span>{" "}
                      {complaint.order.paymentMethod}
                    </p>
                    <p>
                      <span className="font-medium">Trạng thái TT:</span>{" "}
                      {complaint.order.paymentStatus}
                    </p>
                  </div>
                  {complaint.order.request_details && (
                    <div className="md:col-span-2">
                      <p className="font-medium">Chi tiết yêu cầu:</p>
                      <p className="text-sm text-gray-700 mt-1">
                        {complaint.order.request_details}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Video Info */}
          {complaint.video && (
            <>
              <Separator />
              <div>
                <h4 className="text-md font-medium mb-3">Thông tin video</h4>
                <div className="bg-green-50 p-4 rounded-lg grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p>
                      <span className="font-medium">Tiêu đề:</span>{" "}
                      {complaint.video.title}
                    </p>
                    <p>
                      <span className="font-medium">Thời lượng:</span>{" "}
                      {complaint.video.duration}
                    </p>
                    <p>
                      <span className="font-medium">Giá:</span>{" "}
                      {complaint.video.price?.toLocaleString("vi-VN")} VNĐ
                    </p>
                  </div>
                  <div>
                    <p>
                      <span className="font-medium">Trạng thái:</span>{" "}
                      {complaint.video.status}
                    </p>
                    <p>
                      <span className="font-medium">Loại:</span>{" "}
                      {complaint.video.type}
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}

          {complaint.talent && (
            <>
              <Separator />
              <div>
                <h4 className="text-md font-medium mb-3 flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Thông tin Talent
                </h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    {complaint.talent.avatar ? (
                      <img
                        src={complaint.talent.avatar}
                        alt="Avatar"
                        className="w-12 h-12 rounded-full"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                        ?
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{complaint.talent.name}</p>
                      <p className="text-sm text-gray-600">
                        {complaint.talent.nick_name}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{complaint.talent.email || "Chưa có email"}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>
                        {complaint.talent.phone || "Chưa có số điện thoại"}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 col-span-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span>
                        {complaint.talent.address || "Chưa có địa chỉ"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
