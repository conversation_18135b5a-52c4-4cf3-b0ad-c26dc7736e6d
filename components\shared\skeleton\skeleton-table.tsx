import { Skeleton } from "@/components/ui/skeleton";

export function DataTableSkeleton({ rows = 10, columns = 5 }) {
  return (
    <div className="animate-pulse space-y-2 mt-4">
      {[...Array(rows)].map((_, rowIndex) => (
        <div key={rowIndex} className="flex items-center space-x-4">
          {[...Array(columns)].map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-10 w-full" />
          ))}
        </div>
      ))}
    </div>
  );
}
