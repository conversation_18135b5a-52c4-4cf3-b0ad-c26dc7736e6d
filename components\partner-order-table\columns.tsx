import { ColumnDef } from "@tanstack/react-table"
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header"
import { But<PERSON> } from "@/components/ui/button"
import { Link, MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { TalentPaymentHistoryDto } from "@/api/talentPaymentAPI"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Order } from "@/api/models/order/view-order"
import { useRouter } from "next/navigation"

export const getTalentOrderColumns = (): ColumnDef<Order>[] => {

  const router = useRouter();

  return [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Mã đơn hàng" />
      ),
      cell: ({ row }) => <div className="font-medium">{row.getValue("id")}</div>,
    },
    {
      accessorKey: "user",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Người đặt" />
      ),
      cell: ({ row }) => {
        const user = row.original.user;
        return <div>{user?.name}</div>
      },
    },
    {
      accessorKey: "type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Dịch vụ" />
      ),
      cell: ({ row }) => <div>{row.getValue("type")}</div>,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Ngày đặt" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt") as string)
        return <div>{date.toLocaleDateString("vi-VN")}</div>
      },
    },
    {
      accessorKey: "price",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Giá dịch vụ" />
      ),
      cell: ({ row }) => {
        const price = row.getValue("price") as number
        return <div>{price.toLocaleString("vi-VN")} ₫</div>
      },
    },
    // {
    //   accessorKey: "price",
    //   header: ({ column }) => (
    //     <DataTableColumnHeader column={column} title="Thực nhận" />
    //   ),
    //   cell: ({ row }) => {
    //     const amount = row.getValue("price") as number 
    //     const price = row.original.price
    //     const percent = 20 // Assuming a fixed percentage for demonstration
    //     return (
    //       <TooltipProvider>
    //         <Tooltip>
    //           <TooltipTrigger asChild>
    //             <div className="font-medium">{amount.toLocaleString("vi-VN")} ₫</div>
    //           </TooltipTrigger>
    //           <TooltipContent>
    //             <p>{`Công thức: ${price?.toLocaleString("vi-VN")} ₫ x ${percent}%`}</p>
    //           </TooltipContent>
    //         </Tooltip>
    //       </TooltipProvider>
    //     )
    //   },
    // },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Trạng thái" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <div
            className={`capitalize font-semibold ${status === "paid"
                ? "text-green-600"
                : status === "processing"
                  ? "text-blue-500"
                  : status === "pending"
                    ? "text-yellow-500"
                    : "text-gray-500"
              }`}
          >
            {status}
          </div>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const order = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Mở menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Hành động</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(order?.id?.toString() || "")}
              >
                Sao chép mã đơn
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push(`/doi-tac/don-hang/${order?.id}`)}>
                Xem chi tiết
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

}

