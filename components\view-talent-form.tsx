import { useForm } from "react-hook-form";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { User } from "@/api/models/user";
import { ActionConstants, ActionConstantsType } from "@/lib/action-constants";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";

type props = {
  action: ActionConstantsType;
  talent?: User | null;
  onClose?: () => void;
};

export function ViewTalentForm({ action, talent, onClose }: props) {
  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<User>({
    defaultValues: talent || {
      id: undefined,
      name: '',
      email: '',
      phone: '',
      description: '',
      price: 0,
      status: 'active',
      availableFor24hDelivery: false,
      categories: [],
    }
  });

  const imageURL = "https://github.com/shadcn.png"; 
  
  return (
    <form className="overflow-y-auto max-h-[80vh] pr-2">
      <div className="flex items-center space-x-4 mb-4">
        <Avatar className="h-20 w-20 mx-auto">
          <AvatarImage src={imageURL} alt="Talent Avatar" />
          <AvatarFallback>{talent?.name?.[0] ?? "?"}</AvatarFallback>
        </Avatar>
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">Tên</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("name", { required: "Tên là bắt buộc" })} placeholder="Tên talent" />
        {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="nick_name">Biệt danh</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("nick_name")} placeholder="Biệt danh" />
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="email">Email</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("email", { required: "Email là bắt buộc" })} placeholder="Email" />
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="phone">Số điện thoại</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("phone")} placeholder="Số điện thoại (tùy chọn)" />
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="description">Mô tả</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("description")} placeholder="Mô tả" />
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="price">Giá</Label>
        <Input type="number" disabled={action === ActionConstants.VIEW} {...register("price", { valueAsNumber: true })} placeholder="Giá (VNĐ)" />
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="status">Trạng thái</Label>
        <Select disabled={action === ActionConstants.VIEW} defaultValue={talent?.status}>
          <SelectTrigger>
            <SelectValue placeholder="Chọn trạng thái" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="availableFor24hDelivery">Giao hàng trong 24h</Label>
        <Select disabled={action === ActionConstants.VIEW} onValueChange={(val) => setValue("availableFor24hDelivery", val === "true")} defaultValue={talent?.availableFor24hDelivery ? "true" : "false"}>
          <SelectTrigger>
            <SelectValue placeholder="Chọn" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">Có</SelectItem>
            <SelectItem value="false">Không</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="address">Địa chỉ</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("address")} placeholder="Địa chỉ (tùy chọn)" />
      </div>

      <div className="space-y-2 mt-4">
        <Label htmlFor="categories">Danh mục</Label>
        <Select disabled={action === ActionConstants.VIEW}>
          <SelectTrigger>
            <SelectValue placeholder="Chọn danh mục" />
          </SelectTrigger>
          <SelectContent>
            {talent?.categories?.map((cat) => (
              <SelectItem key={cat.id} value={cat.id.toString()}>
                {cat.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="mt-6">
        <button
          type="button"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors"
          onClick={onClose}
        >
          Đóng
        </button>
      </div>
    </form>
    );
}