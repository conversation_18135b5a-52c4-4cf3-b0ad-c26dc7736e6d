import Image from "next/image"
import Link from "next/link"

interface CategoryCardProps {
  name: string
  imageUrl: string
  slug: string
}

export function CategoryCard({ name, imageUrl,slug }: CategoryCardProps) {
  return (
    <Link href={`/danh-muc/${slug}`} className="flex flex-col items-center">
      <div className="relative w-[120px] h-[120px] rounded-full overflow-hidden mb-2">
        <Image src={imageUrl || "/placeholder.svg"} alt={name} width={120} height={120} className="object-cover" />
      </div>
      <span className="text-xs text-center">{name}</span>
    </Link>
  )
}
