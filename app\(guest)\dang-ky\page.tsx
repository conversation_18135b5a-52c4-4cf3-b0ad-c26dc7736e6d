"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Video, Eye, EyeOff, ArrowLeft } from "lucide-react"
import { Controller, useForm } from "react-hook-form"
import { User } from "@/api/models/user"
import { userRegister } from "@/api/authAPI"
import { toast } from "sonner"
import { getErrorMessage } from "@/lib/utils"

export default function RegisterPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [accountType, setAccountType] = useState("user");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { register, handleSubmit, formState: { errors }, reset, control } = useForm<User>({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
    }
  });

  const onSubmit = async (data: User) => {
    setIsLoading(true)

    try {
      const response = await userRegister(data);
      toast.success("Đăng ký thành công! Vui lòng đăng nhập để tiếp tục.");
      router.push("/dang-nhap");
    } catch (error) {
      toast.error(getErrorMessage(error) || "Đăng ký không thành công. Vui lòng thử lại.");
    }
    finally {
      setIsLoading(false);
    }

  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-lg">
        <Card>
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Đăng ký tài khoản</CardTitle>
            <CardDescription>Tạo tài khoản mới để bắt đầu sử dụng Talent</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* <div className="space-y-2">
                <Label htmlFor="accountType">Loại tài khoản</Label>
                <Select value={accountType} onValueChange={setAccountType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn loại tài khoản" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">Khách hàng</SelectItem>
                    <SelectItem value="talent">Talent cá nhân</SelectItem>
                    <SelectItem value="business">Doanh nghiệp</SelectItem>
                  </SelectContent>
                </Select>
              </div> */}

              <div className="space-y-2">
                <Label htmlFor="name">Họ và tên</Label>
                <Input {...register("name", { required: true })} id="name" placeholder="Nguyễn Văn A" />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              
              <div className="space-y-2">
                <Label htmlFor="username">Tên đăng nhập</Label>
                <Input {...register("nick_name", { required: "Vui lòng nhập tên đăng nhập" })} id="username" placeholder="Nguyễn Văn A" />
                {errors.nick_name && (
                  <p className="text-sm text-red-500">{errors.nick_name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input {...register("email", { required: "Vui lòng nhập email" })} id="email" type="email" placeholder="<EMAIL>" />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Số điện thoại</Label>
                <Input
                  {...register("phone", {
                    required: "Vui lòng nhập số điện thoại",
                    pattern: {
                      value: /^(0|\+84)[0-9]{9}$/,
                      message: "Số điện thoại không hợp lệ",
                    },
                  })}
                  id="phone"
                  type="tel"
                  placeholder="0123456789"
                />
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Mật khẩu</Label>
                <div className="relative">
                  <Input {...register("password", {
                    required: "Vui lòng nhập mật khẩu",
                    minLength: {
                      value: 8,
                      message: "Mật khẩu phải có ít nhất 8 ký tự",
                    },
                  })} id="password" type={showPassword ? "text" : "password"} placeholder="Nhập mật khẩu" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Xác nhận mật khẩu</Label>
                <div className="relative">
                  <Input
                    {...register("confirmPassword", { required: "Vui lòng xác nhận mật khẩu", 
                      minLength: {value: 8, 
                      message: "Mật khẩu xác nhận phải có ít nhất 8 ký tự" },
                     })}
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Nhập lại mật khẩu"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
                )}
              </div>

              {(accountType === "talent" || accountType === "business") && (
                <div className="space-y-2">
                  <Label htmlFor="description">Mô tả về bạn/doanh nghiệp</Label>
                  <Textarea
                    {...register("description", { required: true })}
                    id="description"
                    placeholder="Giới thiệu ngắn gọn về bản thân hoặc doanh nghiệp của bạn..."
                    rows={3}
                  />
                </div>
              )}

              <Controller
                name="termsAccepted"
                control={control}
                rules={{ required: "Bạn phải đồng ý với điều khoản" }}
                render={({ field }) => (
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="terms"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                      <Label htmlFor="terms" className="text-sm">
                        Tôi đồng ý với{" "}
                        <Link href="/dieu-khoan-su-dung" className="text-primary hover:underline">
                          Điều khoản sử dụng
                        </Link>{" "}
                        và{" "}
                        <Link href="/chinh-sach-bao-mat" className="text-primary hover:underline">
                          Chính sách bảo mật
                        </Link>
                      </Label>
                    </div>
                    {errors.termsAccepted && (
                      <p className="text-sm text-red-500">{errors.termsAccepted.message}</p>
                    )}
                  </div>
                )}
              />  

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Đang xử lý..." : "Đăng ký"}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm">
              Đã có tài khoản?{" "}
              <Link href="/dang-nhap" className="text-primary hover:underline">
                Đăng nhập ngay
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
