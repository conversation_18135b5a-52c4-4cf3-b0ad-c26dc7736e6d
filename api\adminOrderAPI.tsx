export interface OrderAdminDto {
    orderId: string
    customerName: string
    customerEmail: string
    customerId: string
    talentName: string
    talentAvatarUrl: string
    talentId: string
    serviceName: string
    serviceId: string
    orderDate: string
    completedDate?: string
    status: 'pending' | 'processing' | 'completed' | 'refunded' | 'rejected' | 'complaint'
    price: number
    currency: 'VND' | 'USD'
    paymentMethod: 'vnpay' | 'momo' | 'apple_pay' | 'credit_card' | 'other'
    isVideoSent: boolean
    isComplaint: boolean
    rating?: number
    review?: string
    internalNote?: string
    lastUpdated: string
}

export const getAdminOrders = async (params: any): Promise<{ orders: OrderAdminDto[]; totalItems: number }> => {
    // Simulate fetching data from an API
    const orders: OrderAdminDto[] = Array.from({ length: params.limit }, (_, index) => ({
        orderId: `ORD${index + 1 + (params.page - 1) * params.limit}`,
        customerName: `Customer ${index + 1 + (params.page - 1) * params.limit}`,
        customerEmail: `customer${index + 1 + (params.page - 1) * params.limit}@example.com`,
        customerId: `CUST${index + 1 + (params.page - 1) * params.limit}`,
        talentName: `Talent ${index + 1 + (params.page - 1) * params.limit}`,
        talentAvatarUrl: `https://example.com/talent${index + 1 + (params.page - 1) * params.limit}.jpg`,
        talentId: `TALENT${index + 1 + (params.page - 1) * params.limit}`,
        serviceName: `Service ${index + 1 + (params.page - 1) * params.limit}`,
        serviceId: `SERVICE${index + 1 + (params.page - 1) * params.limit}`,
        orderDate: new Date().toISOString(),
        completedDate: new Date().toISOString(),
        status: 'pending',
        price: 100,
        currency: 'USD',
        paymentMethod: 'credit_card',
        isVideoSent: false,
        isComplaint: false,
        rating: 5,
        review: 'Great service!',
        internalNote: 'Urgent',
        lastUpdated: new Date().toISOString()
    }))
    return {
        orders,
        totalItems: 100
    }
}