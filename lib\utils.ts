import { AxiosError } from "axios";
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getErrorMessage(error: unknown): string {
  let message = "Đã xảy ra lỗi. Vui lòng thử lại.";
  if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<{ message: string }>;
        message = axiosError.response?.data?.message || axiosError.message;
  }
  else if (error instanceof Error) {
    return error.message;
  }
  else if ("message" in (error as any)){
    message = (error as { message: string }).message;
  }
  return message;
}
export const convertNameToNickname = (name : string) => {
  return name
    .toLowerCase()
    .normalize('NFD')                // Tách dấu khỏi ký tự gốc
    .replace(/[\u0300-\u036f]/g, '') // Xoá các dấu
    .replace(/đ/g, 'd')              // Thay đ -> d
    .replace(/[^a-z0-9\s]/g, '')     // Xoá ký tự đặc biệt, giữ chữ cái/số/khoảng trắng
    .trim()
    .replace(/\s+/g, '_');           // Thay khoảng trắng bằng _
}