"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Star, TrendingUp, Clock, Filter } from "lucide-react"
import { User } from "@/api/models/user"
import { getTalents } from "@/api/adminTalentAPI"
import { getErrorMessage } from "@/lib/utils"
import { toast } from "sonner"

import Image from "next/image"

const categories = ["Tất cả", "Ca sĩ", "Diễn viên", "Influencer", "MC", "Người mẫu", "Vận động viên"]

export default function UserHomePage() {
  const [data, setData] = useState<User[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [talentStatus, setTalentStatus] = useState('tat-ca');
  const [open, setOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<User | null>(null);

  const fetchData = async () => {
    try {
      const response = await getTalents({
        page: currentPage,
        limit: 100,
        name: searchTerm || undefined,
        status: talentStatus === 'tat-ca' ? undefined : talentStatus as 'active' | 'inactive' | 'pending',
      });
      setData(response.data);
      setTotalItems(response.meta.total);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải danh sách người dùng');
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm, talentStatus])
  console.log("data", data)

  const [searchQuery, setSearchQuery] = useState("")


  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Chào mừng đến với Talent</h1>
        <p className="text-muted-foreground">Đặt video cá nhân từ người nổi tiếng yêu thích của bạn</p>
      </div>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm talent..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex gap-2">
            <Filter className="h-4 w-4" />
            Lọc
          </Button>
          <Button variant="outline" className="flex gap-2">
            <TrendingUp className="h-4 w-4" />
            Sắp xếp
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {data.map((talent: any, index: number) => (
          <>
            <Link href={`/talent/${talent.nick_name}`}>
              <div className="relative rounded-lg overflow-hidden">
                <div className="absolute top-2 left-2 bg-black/50 rounded-full w-6 h-6 flex items-center justify-center text-xs">
                  {index + 1}
                </div>
                <Image
                  src={talent.avatar || "/placeholder.svg"}
                  alt={talent.name}
                  width={300}
                  height={300}
                  className="w-full object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 text-white p-4">
                  <h3 className="font-bold text-sm">{talent.name}</h3>
                  <p className="text-xs text-gray-300">{talent.job}</p>
                </div>
              </div>
              <div className="mt-2 flex justify-between items-center">
                <span className="font-bold text-sm">{talent.price}</span>
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-500 stroke-none" />
                  <span className="text-xs">{talent.rating}</span>
                  <span className="text-xs text-gray-400">(5)</span>
                </div>
              </div>
            </Link>
          </>
        ))}
      </div>
    </div>
  )
}
