"use client";

import { useUserStore } from "@/hooks/useUserStore"
import { deleteCookie } from "cookies-next";
import { useRouter } from "next/navigation";
import { useEffect } from "react"

export default function LogoutPage() {

  const router = useRouter();

  useEffect(() => {
    deleteCookie("user");
    useUserStore.getState().clearUser();
    localStorage.removeItem("user-storage");
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    router.push("/");
  }, [])

  return (
    <div>
      <h1><PERSON><PERSON> đăng xuất</h1>
    </div>
  )
}
