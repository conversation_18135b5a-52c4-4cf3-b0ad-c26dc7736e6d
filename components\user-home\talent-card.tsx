import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"

interface TalentCardProps {
  rank: number
  name: string
  role: string
  price: string
  nick_name: string
  rating: number
  reviews: number
  imageUrl: string
}

export function TalentCard({ rank, name, role, price, nick_name, rating, reviews, imageUrl }: TalentCardProps) {
  return (
    <Link href={`/talent/${nick_name}`} className="min-w-[200px] max-w-[200px]">
      <div className="relative rounded-lg overflow-hidden">
        <div className="absolute top-2 left-2 bg-black/50 rounded-full w-6 h-6 flex items-center justify-center text-xs">
          {rank}
        </div>
        <Image
          src={imageUrl || "/placeholder.svg"}
          alt={name}
          width={200}
          height={300}
          className="w-full aspect-[2/3] object-cover"
        />
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          <h3 className="font-bold text-sm">{name}</h3>
          <p className="text-xs text-gray-300">{role}</p>
        </div>
      </div>
      <div className="mt-2 flex justify-between items-center">
        <span className="font-bold text-sm">{price}</span>
        <div className="flex items-center gap-1">
          <Star className="h-3 w-3 fill-yellow-500 stroke-none" />
          <span className="text-xs">{rating}</span>
          <span className="text-xs text-gray-400">({reviews})</span>
        </div>
      </div>
    </Link>
  )
}
