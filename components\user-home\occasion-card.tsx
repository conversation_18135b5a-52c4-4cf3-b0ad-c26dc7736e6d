import Link from "next/link"

interface OccasionCardProps {
  title: string
  description: string
  imageUrl: string
}

export function OccasionCard({ title, description, imageUrl }: OccasionCardProps) {


  return (
    <div
      className={`rounded-lg overflow-hidden p-4 h-40 flex flex-col justify-between`}
      style={{
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundBlendMode: "overlay",
      }}
    >
      <div className="text-sm text-white">{description}</div>
      <div className="text-xl font-bold text-white mb-auto">{title}</div>
    </div>
  )
}
