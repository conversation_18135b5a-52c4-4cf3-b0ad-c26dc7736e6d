import { User } from "./user"

export interface Permission {
  id: number
  name: string
  description: string | null
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: number
  name: string
  description: string | null
  createdAt: string
  updatedAt: string
  isDeleted: boolean
  permissions: Permission[]
}

export interface AuthResponse {
  user: User
  token: string
}

export interface LoginRequest {
  email: string
  password: string
}
