import { Category } from "@/api/models/category";
import { useForm } from "react-hook-form";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import { createCategory, getAllCategories, updateCategory } from "@/api/categoryAPI";
import { useEffect, useState } from "react";
import { getErrorMessage } from "@/lib/utils";
import { ActionConstants, ActionConstantsType } from "@/lib/action-constants";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { uploadFile } from "@/api/uploadFileAPI";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { ca } from "date-fns/locale";

type Props = {
  onComplete?: (category : Category) => void;
  category?: Category | null;
  action?: ActionConstantsType;
}

export default function CreateCategoryForm({ onComplete, category, action }: Props) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<Category>({
    defaultValues: {
      id: category?.id || undefined,
      name: category?.name || '',
      slug: category?.slug || '',
      description: category?.description || '',
      thumbnail: category?.thumbnail || '',
      parent: category?.parent ? (typeof category.parent === 'number' ? category.parent : category.parent.id) : undefined,
      createdAt: category?.createdAt || undefined,
      updatedAt: category?.updatedAt || undefined,
    },
  });

  const [imageURL, setImageURL] = useState<string | null>(category?.thumbnail || null);

  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    fetchCategories();
  }, []);

  const onSubmit = async (data: Category) => {
    setError(null); // Reset error state before submission
    try {
      data.createdAt = undefined;
      data.updatedAt = undefined;
      if (imageURL) {
        data.thumbnail = imageURL; 
      } else {
        data.thumbnail = ''; 
      }
      var response : Category | undefined = undefined;
      if (action === ActionConstants.CREATE) {
         response = await createCategory(data);
         toast.success('Tạo danh mục thành công');
      }
      else {
        response = await updateCategory(data);
        toast.success('Cập nhật danh mục thành công');
      }

      if (onComplete) {
        onComplete(response);
      }
    }
    catch (error) {
      setError(getErrorMessage(error));
    } finally {
      reset(); // Reset the form after submission
    }
  }

  const fetchCategories = async () => {
    try {
      var response = await getAllCategories();
      if (action === ActionConstants.UPDATE) {
        response = response.filter(ca => ca.id !== category?.id);
      }
      setCategories(response);
    } catch (error) {
      toast.error(getErrorMessage(error) || 'Không thể tải danh mục cha');
    }
  }


  const handleOnImageChange = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const url = await uploadFile(file);
      setImageURL(url);
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className ="flex items-center space-x-4 mb-4">
        <Avatar className="h-20 w-20 mx-auto">
          <AvatarImage src={imageURL || "https://github.com/shadcn.png"} alt="Category Image" />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
      </div>
      <div className="space-y-2">
        <Label htmlFor="name">Tên danh mục</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("name", {required: "Tên danh mục là bắt buộc"})} placeholder="Tên danh mục" required />
      </div>
      <div className="space-y-2 mt-4">
        <Label htmlFor="slug">Slug</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("slug")} placeholder="Slug (tùy chọn)" />
      </div>
      <div className="space-y-2 mt-4">
        <Label htmlFor="description">Mô tả</Label>
        <Input disabled={action === ActionConstants.VIEW} {...register("description")} placeholder="Mô tả (tùy chọn)" />
      </div>
      <div className="space-y-2 mt-4">
        <Label htmlFor="thumbnail">Ảnh đại diện</Label>
        <Input type="file" disabled={action === ActionConstants.VIEW} {...register("thumbnail")} placeholder="URL ảnh đại diện (tùy chọn)" onChange={(e) =>handleOnImageChange(e)} />
      </div>
      <div className="space-y-2 mt-4">
        <Label htmlFor="parent">Danh mục cha</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Chọn danh mục cha" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id.toString()}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* <Input  disabled={action === ActionConstants.VIEW} {...register("parent", { valueAsNumber: true })} placeholder="ID danh mục cha (tùy chọn)" type="number" /> */}
      </div>
      <div className="mt-6">
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors"
          disabled={action === ActionConstants.VIEW}
        >
          {action === ActionConstants.CREATE ? "Tạo danh mục" : "Cập nhật danh mục"}
        </button>
      </div>
      {errors.name && <p className="text-red-500 text-sm mt-2">{errors.name.message}</p>}
      {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
    </form>
  )
}