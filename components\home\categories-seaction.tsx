import React from 'react'
import { ArrowRight } from 'lucide-react'
import { getAllCategories } from '@/api/categoryAPI'
import Link from 'next/link'
import Image from 'next/image'

export default async function CategoriesSeaction() {
    const categories = await getAllCategories()
    return (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-8 gap-6 pb-6">
            {categories.slice(0, 7).map((category) => (
                <Link
                    key={category.id}
                    href={`/danh-muc/${category.slug}`}
                    className="flex flex-col items-center hover:scale-105 transition-transform"
                >
                    <div className="relative w-[120px] h-[120px] rounded-full overflow-hidden mb-2 shadow-md">
                        <Image
                            src={category.thumbnail || '/placeholder.svg?height=100&width=100'}
                            alt={category.name}
                            width={120}
                            height={120}
                            className="object-cover"
                        />
                    </div>
                    <span className="text-sm text-center font-medium">{category.name}</span>
                </Link>
            ))}

            <Link
                href="/"
                className="flex flex-col items-center justify-center gap-2 w-[120px] h-[120px] rounded-full bg-gray-800 border border-white text-white hover:bg-gray-700 transition-all"
            >
                <ArrowRight size={20} />
                <span className="text-xs">Xem tất cả</span>
            </Link>
        </div>
    )
}
