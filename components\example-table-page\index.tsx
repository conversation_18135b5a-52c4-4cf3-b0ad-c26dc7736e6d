"use client"
import { useState, useEffect } from 'react'
import { DataTable } from '@/components/shared/data-table'
import { columns } from './columns'
import { Button } from '@/components/custom/button'
import { toast } from '@/components/ui/use-toast'
import { getUsers } from '@/api/userAPI'

export default function ExampleTablePage() {
  const [data, setData] = useState([])
  const [totalItems, setTotalItems] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')

  const fetchData = async () => {
    try {
      const response = await getUsers({
        page: currentPage,
        limit: pageSize,
        search: searchTerm
      })
      setData(response.users)
      setTotalItems(response.totalItems)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch data',
        variant: 'destructive'
      })
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, pageSize, searchTerm])

  const toolbarComponent = (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <input
          placeholder="Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          className="h-8"
        >
          Export
        </Button>
      </div>
    </div>
  )
  return (
      <div className='flex flex-col'>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Items</h1>
        </div>
        <DataTable
          columns={columns}
          data={data}
          manualPagination={true}
          totalItems={totalItems}
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          onSearchChange={setSearchTerm}
          toolbarComponent={toolbarComponent}
        />
      </div>
  )
}