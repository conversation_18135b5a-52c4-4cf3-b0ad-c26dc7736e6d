"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Video, ArrowLeft, Search, MessageCircle, Mail } from "lucide-react"
import { useState } from "react"

const faqData = [
  {
    category: "Tổng quan",
    questions: [
      {
        question: "TalentVid là gì?",
        answer:
          "TalentVid là nền tảng kết nối khách hàng với các talent để đặt video cá nhân hóa. Bạn có thể đặt video chúc mừng, qu<PERSON>ng cáo, hay bất kỳ nội dung video nào từ các talent yêu thích.",
      },
      {
        question: "<PERSON><PERSON><PERSON> thế nào để đặt video?",
        answer:
          "Đơn giản! Chỉ cần: 1) <PERSON>ọn talent yêu thích, 2) <PERSON><PERSON><PERSON><PERSON> thông tin yêu cầu video, 3) <PERSON><PERSON> toán, 4) <PERSON><PERSON> talent thực hiện và gửi video cho bạn.",
      },
      {
        question: "Mất bao lâu để nhận được video?",
        answer:
          "Thời gian hoàn thành video thường từ 3-7 ngày làm việc, tùy thuộc vào độ phức tạp và lịch trình của talent. Thời gian cụ thể sẽ được hiển thị trên profile của từng talent.",
      },
    ],
  },
  {
    category: "Thanh toán",
    questions: [
      {
        question: "Các phương thức thanh toán nào được hỗ trợ?",
        answer:
          "Chúng tôi hỗ trợ thanh toán qua thẻ tín dụng/ghi nợ, ví điện tử (MoMo, ZaloPay), và chuyển khoản ngân hàng.",
      },
      {
        question: "Khi nào tôi được hoàn tiền?",
        answer:
          "Bạn sẽ được hoàn tiền 100% nếu talent từ chối đơn hàng hoặc không hoàn thành trong thời hạn cam kết. Hoàn tiền sẽ được xử lý trong 3-5 ngày làm việc.",
      },
      {
        question: "Có thể hủy đơn hàng không?",
        answer:
          "Bạn có thể hủy đơn hàng miễn phí trong vòng 1 giờ sau khi đặt. Sau thời gian này, việc hủy sẽ phụ thuộc vào trạng thái đơn hàng và chính sách của talent.",
      },
    ],
  },
  {
    category: "Cho Talent",
    questions: [
      {
        question: "Làm thế nào để trở thành talent?",
        answer:
          "Bạn cần đăng ký tài khoản talent, hoàn thiện profile với video giới thiệu, thiết lập giá cả và dịch vụ. Sau khi được admin phê duyệt, bạn có thể bắt đầu nhận đơn hàng.",
      },
      {
        question: "TalentVid lấy bao nhiều phần trăm hoa hồng?",
        answer:
          "Chúng tôi lấy 15% hoa hồng từ mỗi đơn hàng hoàn thành. Talent sẽ nhận 85% giá trị đơn hàng sau khi khách hàng xác nhận hài lòng.",
      },
      {
        question: "Khi nào talent nhận được tiền?",
        answer:
          "Tiền sẽ được chuyển vào tài khoản talent sau 3 ngày kể từ khi khách hàng xác nhận hài lòng, hoặc tự động sau 7 ngày nếu không có phản hồi từ khách hàng.",
      },
    ],
  },
  {
    category: "Hỗ trợ",
    questions: [
      {
        question: "Làm thế nào để liên hệ hỗ trợ?",
        answer:
          "Bạn có thể liên hệ qua: 1) Chat trực tuyến trên website, 2) Email: <EMAIL>, 3) Hotline: 1900-xxxx (8:00-22:00 hàng ngày).",
      },
      {
        question: "Nếu không hài lòng với video thì sao?",
        answer:
          "Nếu video không đúng yêu cầu, bạn có thể yêu cầu talent chỉnh sửa (1 lần miễn phí) hoặc khiếu nại để được hỗ trợ hoàn tiền.",
      },
    ],
  },
]

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredFAQ = faqData
    .map((category) => ({
      ...category,
      questions: category.questions.filter(
        (q) =>
          q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          q.answer.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
    }))
    .filter((category) => category.questions.length > 0)

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Câu hỏi thường gặp</h1>
            <p className="text-xl text-muted-foreground mb-6">
              Tìm câu trả lời cho những thắc mắc phổ biến về TalentVid
            </p>

            {/* Search */}
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Tìm kiếm câu hỏi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* FAQ Content */}
          <div className="space-y-6">
            {filteredFAQ.map((category, categoryIndex) => (
              <Card key={categoryIndex}>
                <CardHeader>
                  <CardTitle className="text-xl">{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {category.questions.map((faq, index) => (
                      <AccordionItem key={index} value={`item-${categoryIndex}-${index}`}>
                        <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                        <AccordionContent className="text-muted-foreground">{faq.answer}</AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredFAQ.length === 0 && searchTerm && (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">Không tìm thấy câu hỏi nào phù hợp với "{searchTerm}"</p>
              </CardContent>
            </Card>
          )}

          {/* Contact Support */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Vẫn cần hỗ trợ?
              </CardTitle>
              <CardDescription>Không tìm thấy câu trả lời? Liên hệ với đội ngũ hỗ trợ của chúng tôi</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button className="w-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Chat trực tuyến
                </Button>
                <Button variant="outline" className="w-full">
                  <Mail className="h-4 w-4 mr-2" />
                  Gửi email hỗ trợ
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
