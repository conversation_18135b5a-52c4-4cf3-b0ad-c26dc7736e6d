import { Complaint } from "@/api/complaintsAPI";
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header";
import { ColumnDef, Row } from "@tanstack/react-table";
import ActionsDropdown from "./actionsDropdown";


// Component for user cell
const UserCell = ({ row }: { row: Row<Complaint> }) => {
  const user = row.original.user;
  return (
    <div className="flex items-center gap-2">
      {user?.avatar && (
        <img src={user.avatar} alt="avatar" className="w-6 h-6 rounded-full" />
      )}
      <span>{user?.name}</span>
    </div>
  );
};

// Component for complaint type cell
const ComplaintTypeCell = ({ row }: { row: Row<Complaint> }) => {
  const type = row.getValue("complaintType") as string;
  const typeLabels = {
    video_quality: "Chất lượng video",
    delivery_time: "Thời gian giao hàng",
    content_issue: "Vấn đề nội dung",
    payment_issue: "Vấn đề thanh toán",
    other: "Khác",
  };
  return <div>{typeLabels[type as keyof typeof typeLabels] || type}</div>;
};

// Component for priority cell
const PriorityCell = ({ row }: { row: Row<Complaint> }) => {
  const priority = row.getValue("priority") as string;
  const priorityColors = {
    low: "text-gray-500",
    medium: "text-yellow-500",
    high: "text-orange-500",
    urgent: "text-red-600",
  };
  const priorityLabels = {
    low: "Thấp",
    medium: "Trung bình",
    high: "Cao",
    urgent: "Khẩn cấp",
  };
  return (
    <div
      className={`font-semibold ${
        priorityColors[priority as keyof typeof priorityColors]
      }`}
    >
      {priorityLabels[priority as keyof typeof priorityLabels] || priority}
    </div>
  );
};

// Component for status cell
const StatusCell = ({ row }: { row: Row<Complaint> }) => {
  const status = row.getValue("status") as string;
  const statusColors = {
    pending: "text-yellow-500",
    investigating: "text-indigo-500",
    processing: "text-blue-500",
    resolved: "text-green-600",
    rejected: "text-red-600",
  };
  const statusLabels = {
    pending: "Chờ xử lý",
    processing: "Đang xử lý",
    investigating : 'Đang điều tra',
    resolved: "Đã giải quyết",
    rejected: "Từ chối",
  };
  return (
    <div
      className={`capitalize font-semibold ${
        statusColors[status as keyof typeof statusColors]
      }`}
    >
      {statusLabels[status as keyof typeof statusLabels] || status}
    </div>
  );
};

export const adminComplaintColumns: ColumnDef<Complaint>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mã khiếu nại" />
    ),
    cell: ({ row }) => <div className="font-medium">{row.getValue("id")}</div>,
  },
  {
    accessorKey: "orderId",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mã đơn hàng" />
    ),
    cell: ({ row }) => <div>{row.getValue("orderId")}</div>,
  },
  {
    accessorKey: "user.name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Người dùng" />
    ),
    cell: ({ row }) => <UserCell row={row} />,
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tiêu đề" />
    ),
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("title")}</div>
    ),
  },
  {
    accessorKey: "complaintType",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Loại khiếu nại" />
    ),
    cell: ({ row }) => <ComplaintTypeCell row={row} />,
  },
  {
    accessorKey: "priority",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mức độ ưu tiên" />
    ),
    cell: ({ row }) => <PriorityCell row={row} />,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Ngày khiếu nại" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt"));
      return <div>{date.toLocaleDateString("vi-VN")}</div>;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Trạng thái" />
    ),
    cell: ({ row }) => <StatusCell row={row} />,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "description",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mô tả" />
    ),
    cell: ({ row }) => (
      <div className="line-clamp-2">{row.getValue("description")}</div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionsDropdown complaint={row.original} />,
  },
];
