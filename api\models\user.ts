import { Category } from "./category";

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  createdAt: string;
  updatedAt: string; 
  availableFor24hDelivery: boolean;
  description: string;
  tags: string[];
  price: number;
  address: string | null;
  categories: Category[] | any[]; 
  videos: any[]; 
  status : 'active' | 'inactive' | 'pending';
  nick_name: string | null;
  password?: string;
  confirmPassword?: string;
  termsAccepted?: boolean;
  job?: string | null;
  roles?: {
    id: number;
    name: string;
    description: string | null;
    createdAt: string;
    updatedAt: string;
  }[];
  avatar?: string | null;
}