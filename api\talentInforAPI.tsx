export interface TalentPublicProfile {
  displayName: string
  avatarUrl: string
  coverImageUrl?: string
  introVideoUrl?: string
  bio: string
  longDescription?: string
  categories: string[] // hoặc enum nếu bạn muốn giới hạn lựa chọn
  occupation: string
  languageSpoken?: string[]
  location?: string
  birthDate?: string // ISO date string
  socialLinks?: {
    facebook?: string
    instagram?: string
    twitter?: string
    youtube?: string
    linkedin?: string
  }
  website?: string
  phoneNumber?: string
  email?: string
}

export const getTalentProfileForEdit = async (talentId: string): Promise<TalentPublicProfile> => {
  // dummy data
  return {
    displayName: "John Doe",
    avatarUrl: "https://example.com/avatar.jpg",
    coverImageUrl: "https://example.com/cover.jpg",
    introVideoUrl: "https://example.com/intro.mp4",
    bio: "Lorem ipsum dolor sit amet.",
    longDescription: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    categories: ["Music", "Art"],
    occupation: "Musician",
    languageSpoken: ["English", "Spanish"],
    location: "New York, USA",
    birthDate: "1990-01-01",
    socialLinks: {
      facebook: "https://facebook.com/johndoe",
      instagram: "https://instagram.com/johndoe",
      twitter: "https://twitter.com/johndoe",
      youtube: "https://youtube.com/johndoe",
      linkedin: "https://linkedin.com/in/johndoe"
    },
    website: "https://johndoe.com",
    phoneNumber: "+1234567890",
    email: "<EMAIL>"
  };
}