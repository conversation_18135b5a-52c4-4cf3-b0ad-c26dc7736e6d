import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/custom/button'
import { Input } from '@/components/ui/input'
import { DataTableViewOptions } from './data-table-view-options'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onCreateItem?: (newItem: any) => void
  onSearchChange?: (searchTerm: string) => void
  filterComponent?: React.ReactNode
}

export function DataTableToolbar<TData>({
  table,
  onCreateItem,
  onSearchChange,
  filterComponent,
}: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState('')
  const isFiltered = table.getState().columnFilters.length > 0

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
    if (onSearchChange) {
      onSearchChange(value)
    } else {
      table.getColumn('name')?.setFilterValue(value)
    }
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Search..."
          value={searchValue}
          onChange={handleSearch}
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {filterComponent}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        {onCreateItem && (
          <Button
            variant="default"
            size="sm"
            className="h-8"
            onClick={() => onCreateItem({})}
          >
            Create
          </Button>
        )}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}