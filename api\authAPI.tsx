import api from "./axios"
import { AuthResponse, LoginRequest } from "./models/auth"
import { User } from "./models/user";

export const apiLogin = async (credentials: LoginRequest): Promise<AuthResponse> => {
  const response = await api.post("/auth/login", credentials);
  return response.data as AuthResponse;
};

export const userRegister = async (data: User): Promise<User> => {
  const response = await api.post("/auth/register", data);
  return response.data as User;
};