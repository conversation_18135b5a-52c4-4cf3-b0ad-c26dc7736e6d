import type { ReactNode } from "react"

interface HowItWorksCardProps {
  icon: ReactNode
  title: string
  description: string
}

export function HowItWorksCard({ icon, title, description }: HowItWorksCardProps) {
  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <div className="bg-gray-800 rounded-full w-10 h-10 flex items-center justify-center mb-4 text-white">{icon}</div>
      <h3 className="font-bold mb-2">{title}</h3>
      <p className="text-sm text-gray-400">{description}</p>
    </div>
  )
}
