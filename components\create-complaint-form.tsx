"use client";
import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useForm, Controller } from "react-hook-form";
import { createComplaint } from "@/api/complaintsAPI";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  ComplaintPriority,
  ComplaintType,
} from "@/components/admin-complain-table";
import { cn } from "@/lib/utils";
import { useParams, useSearchParams } from "next/navigation";

interface FormData {
  title: string;
  description: string;
  complaintType: string;
  priority: string;
  orderId: number;
  videoId: number;
}

export default function CreateComplaintForm({
  onSuccess,
}: {
  onSuccess?: () => void;
}) {
  const searchParams = useSearchParams();
  const orderId : any = searchParams.get("orderid");
  const videoId : any  = searchParams.get("videoid");

  const [files, setFiles] = useState<File[]>([]);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      title: "",
      description: "",
      complaintType: "",
      priority: "",
      orderId: orderId,
      videoId: videoId,
    },
  });

  const mutation = useMutation<any, Error, FormData>({
    mutationFn: async (formData: any) => createComplaint(formData),
    onSuccess: () => {
      alert("Tạo khiếu nại thành công!");
      reset();
      setFiles([]);
    },
    onError: () => {
      alert("Có lỗi xảy ra!");
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files));
    }
  };

  const onSubmit = (data: FormData) => {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, String(value));
    });
    console.log("files", files);

    files.forEach((file) => {
      formData.append("evidence_file", file);
    });

    console.log("formData", formData);

    mutation.mutate(formData as any);
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={cn(
        "max-w-xl mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow-lg p-8 space-y-6",
        "flex flex-col gap-1"
      )}
    >
      <h2 className="text-2xl font-bold text-center mb-2">
        Tạo khiếu nại đơn hàng
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="title">Tiêu đề</Label>
          <Controller
            name="title"
            control={control}
            rules={{ required: "Tiêu đề là bắt buộc" }}
            render={({ field }) => (
              <Input
                {...field}
                id="title"
                placeholder="Nhập tiêu đề"
                className={errors.title ? "border-red-500" : ""}
              />
            )}
          />
          {errors.title && (
            <span className="text-red-500 text-sm">{errors.title.message}</span>
          )}
        </div>
        <div>
          <Label htmlFor="priority">Độ ưu tiên</Label>
          <Controller
            name="priority"
            control={control}
            rules={{ required: "Độ ưu tiên là bắt buộc" }}
            render={({ field }) => (
              <select
                {...field}
                id="priority"
                className={cn(
                  "w-full border rounded px-2 py-2 bg-transparent",
                  errors.priority ? "border-red-500" : ""
                )}
              >
                <option value="">Chọn độ ưu tiên</option>
                <option value={ComplaintPriority.LOW}>Thấp</option>
                <option value={ComplaintPriority.MEDIUM}>Trung bình</option>
                <option value={ComplaintPriority.HIGH}>Cao</option>
                <option value={ComplaintPriority.URGENT}>Khẩn cấp</option>
              </select>
            )}
          />
          {errors.priority && (
            <span className="text-red-500 text-sm">
              {errors.priority.message}
            </span>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="complaintType">Loại khiếu nại</Label>
        <Controller
          name="complaintType"
          control={control}
          rules={{ required: "Loại khiếu nại là bắt buộc" }}
          render={({ field }) => (
            <select
              {...field}
              id="complaintType"
              className={cn(
                "w-full border rounded px-2 py-2 bg-transparent",
                errors.complaintType ? "border-red-500" : ""
              )}
            >
              <option value="">Chọn loại khiếu nại</option>
              <option value={ComplaintType.VIDEO_QUALITY}>
                Chất lượng video
              </option>
              <option value={ComplaintType.DELIVERY_TIME}>
                Thời gian giao hàng
              </option>
              <option value={ComplaintType.CONTENT_ISSUE}>
                Vấn đề nội dung
              </option>
              <option value={ComplaintType.PAYMENT_ISSUE}>
                Vấn đề thanh toán
              </option>
              <option value={ComplaintType.OTHER}>Khác</option>
            </select>
          )}
        />
        {errors.complaintType && (
          <span className="text-red-500 text-sm">
            {errors.complaintType.message}
          </span>
        )}
      </div>
      <div>
        <Label htmlFor="description">Mô tả chi tiết</Label>
        <Controller
          name="description"
          control={control}
          rules={{ required: "Mô tả là bắt buộc" }}
          render={({ field }) => (
            <Textarea
              {...field}
              id="description"
              placeholder="Nhập mô tả chi tiết"
              className={cn(
                "min-h-[80px]",
                errors.description ? "border-red-500" : ""
              )}
            />
          )}
        />
        {errors.description && (
          <span className="text-red-500 text-sm">
            {errors.description.message}
          </span>
        )}
      </div>
      <div>
        <Label htmlFor="evidence_file">
          Tệp minh chứng (có thể chọn nhiều)
        </Label>
        <Input
          id="evidence_file"
          type="file"
          multiple
          onChange={handleFileChange}
          accept="image/*,video/*"
        />
        {files.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-2">
            {files.map((file, idx) => (
              <span
                key={idx}
                className="text-xs bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded"
              >
                {file.name}
              </span>
            ))}
          </div>
        )}
      </div>
      <Button type="submit" disabled={mutation.isPending} className="w-full">
        {mutation.isPending ? "Đang gửi..." : "Gửi khiếu nại"}
      </Button>
      {mutation.isError && (
        <div className="text-red-500 text-center">
          Có lỗi xảy ra, vui lòng thử lại.
        </div>
      )}
    </form>
  );
}
