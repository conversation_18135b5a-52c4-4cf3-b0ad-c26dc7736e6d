"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import Image from "next/image"
import { useState } from "react"

export default function TalentServiceDetailForm() {

  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [state, setState] = useState({
    isActive: true,
  });
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      setAvatarPreview(URL.createObjectURL(file));
    }
  };
  const [introVideoPreview, setIntroVideoPreview] = useState<string | null>(null);
  const [introVideoFile, setIntroVideoFile] = useState<File | null>(null);
  const handleIntroVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIntroVideoFile(file);
      setIntroVideoPreview(URL.createObjectURL(file));
    }
  }

  const handleIsActiveChange = (checked: boolean) => {
    setState((prev) => ({
      ...prev,
      isActive: checked,
    }));
  }

  return (
    <>
      <Separator className="my-6" />
      <h3 className="text-lg font-semibold">Chỉnh sửa dịch vụ Talent</h3>

      <div className="space-y-2 text-center">
        <Label htmlFor="avatarUpload" className="block text-lg font-medium">
          Ảnh đại diện
        </Label>
        <div className="relative inline-block w-40 h-40 rounded-full overflow-hidden bg-gray-200 mx-auto group cursor-pointer">
          {avatarPreview ? (
            <Image src={avatarPreview} alt="Avatar Preview" fill className="object-cover" />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-sm text-muted-foreground">
              No Image
            </div>
          )}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 flex items-center justify-center transition">
            <span className="text-white text-sm font-medium">Upload</span>
          </div>
          <input
            id="avatarUpload"
            type="file"
            accept="image/*"
            onChange={handleAvatarChange}
            className="absolute inset-0 opacity-0 cursor-pointer"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="serviceTitle">Tên dịch vụ</Label>
        <Input id="serviceTitle" placeholder="VD: Gửi lời chúc sinh nhật" required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="serviceDescription">Mô tả dịch vụ</Label>
        <Textarea id="serviceDescription" rows={3} placeholder="Mô tả ngắn gọn về dịch vụ" required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="price">Giá dịch vụ</Label>
        <Input id="price" type="number" placeholder="VD: 500000" required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="deliveryTime">Thời gian giao</Label>
        <Input id="deliveryTime" placeholder="VD: 3 ngày" required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="sampleVideos">Video mẫu (nếu có)</Label>
        <Input id="sampleVideos" type="file" multiple accept="video/*" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="introVideoUpload">Video giới thiệu</Label>
        <input
          id="introVideoUpload"
          type="file"
          accept="video/*"
          onChange={handleIntroVideoChange}
          className="block w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90"
        />
        {introVideoPreview && (
          <video src={introVideoPreview} controls className="mt-2 w-full rounded-md border" />
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="tags">Tags (phân cách bằng dấu phẩy)</Label>
        <Input id="tags" placeholder="VD: sinh nhật, lời chúc, người nổi tiếng" />
      </div>

      <div className="flex items-center justify-between space-y-2">
        <Label htmlFor="isActive">Dịch vụ đang hoạt động</Label>
        <Switch
          id="isActive"
          checked={state.isActive}
          onCheckedChange={handleIsActiveChange}
        />
      </div>
    </>
  )
}
