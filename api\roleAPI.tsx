import api from "./axios";
import { Paginator } from "./models/paginator";
import { Role } from "./models/role";

export const getRoles = async (page: number, limit: number): Promise<Paginator<Role>> => {
  const response = await api.get<Paginator<Role>>("/roles", {
    params: { page, limit }
  });
  return response.data;
};

export const fetchAllRoles = async (): Promise<Role[]> => {
  const response = await api.get<Role[]>("/roles");
  return response.data;
};

export const createRole = async (roleData: Partial<Role>): Promise<Role> => {
  const response = await api.post<Role>("/roles", roleData);
  return response.data;
};

export const updateRole = async (roleData: Partial<Role>): Promise<Role> => {
  const response = await api.patch<Role>("/roles", roleData);
  return response.data;
};

export const deleteRole = async (roleId: number): Promise<void> => {
  await api.delete("/roles", { data: { id: roleId } });
};

export const getRoleById = async (id: number): Promise<Role> => {
  const response = await api.get<Role>(`/roles/${id}`);
  return response.data;
};
