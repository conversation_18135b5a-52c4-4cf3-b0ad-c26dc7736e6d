"use client";
import Image from "next/image";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

export default function PartnerProfilePage() {


  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      setAvatarPreview(URL.createObjectURL(file));
    }
  };

  const [introVideoPreview, setIntroVideoPreview] = useState<string | null>(null);
  const [introVideoFile, setIntroVideoFile] = useState<File | null>(null);

  const handleIntroVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIntroVideoFile(file);
      setIntroVideoPreview(URL.createObjectURL(file));
    }
  };

  return (
    <>
      <Separator className="my-6" />
      <h3 className="text-lg font-semibold">Thông tin hồ sơ Talent</h3>

      <div className="space-y-2 text-center">
        <Label htmlFor="avatarUpload" className="block text-lg font-medium">
          Ảnh đại diện
        </Label>

        <div className="relative inline-block w-40 h-40 rounded-full overflow-hidden bg-gray-200 mx-auto group cursor-pointer">
          {avatarPreview ? (
            <Image
              src={avatarPreview}
              alt="Avatar Preview"
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-sm text-muted-foreground">
              No Image
            </div>
          )}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 flex items-center justify-center transition">
            <span className="text-white text-sm font-medium">Upload</span>
          </div>
          <input
            id="avatarUpload"
            type="file"
            accept="image/*"
            onChange={handleAvatarChange}
            className="absolute inset-0 opacity-0 cursor-pointer"
          />
        </div>
      </div>


      <div className="space-y-2">
        <Label htmlFor="displayName">Tên hiển thị</Label>
        <Input id="displayName" placeholder="VD: Anh Bé" required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="coverImageUrl">Ảnh bìa</Label>
        <Input id="coverImageUrl" type="url" placeholder="URL ảnh bìa (nếu có)" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="introVideoUpload">Video giới thiệu</Label>

        <input
          id="introVideoUpload"
          type="file"
          accept="video/*"
          onChange={handleIntroVideoChange}
          className="block w-full text-sm file:mr-4 file:py-2 file:px-4
               file:rounded-md file:border-0
               file:text-sm file:font-semibold
               file:bg-primary file:text-white
               hover:file:bg-primary/90"
        />

        {introVideoPreview && (
          <video
            src={introVideoPreview}
            controls
            className="mt-2 w-full rounded-md border"
          />
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">Giới thiệu ngắn</Label>
        <Textarea id="bio" placeholder="Tối đa 300 ký tự" rows={3} maxLength={300} required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="longDescription">Mô tả chi tiết</Label>
        <Textarea id="longDescription" placeholder="Mô tả chi tiết hơn về bạn..." rows={4} />
      </div>

      <div className="space-y-2">
        <Label htmlFor="occupation">Nghề nghiệp</Label>
        <Input id="occupation" placeholder="VD: Diễn viên, Ca sĩ..." required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="categories">Danh mục tham gia</Label>
        <Input id="categories" placeholder="VD: Diễn viên, Streamer (phân cách bằng dấu phẩy)" required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="languageSpoken">Ngôn ngữ sử dụng</Label>
        <Input id="languageSpoken" placeholder="VD: Tiếng Việt, English (phân cách bằng dấu phẩy)" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="location">Địa điểm</Label>
        <Input id="location" placeholder="Hà Nội, Việt Nam" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="birthDate">Ngày sinh</Label>
        <Input id="birthDate" type="date" />
      </div>

      <div className="space-y-2">
        <Label>Liên kết mạng xã hội</Label>
        <div className="grid grid-cols-2 gap-4">
          <Input id="facebook" placeholder="Facebook URL" />
          <Input id="instagram" placeholder="Instagram URL" />
          <Input id="twitter" placeholder="Twitter URL" />
          <Input id="youtube" placeholder="YouTube URL" />
          <Input id="linkedin" placeholder="LinkedIn URL" />
          <Input id="website" placeholder="Website cá nhân" />
        </div>
      </div>
    </>
  );
} 