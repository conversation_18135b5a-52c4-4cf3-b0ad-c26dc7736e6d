"use client";

import { SelectContent, SelectValue } from "@radix-ui/react-select";
import { Label } from "../ui/label";
import { Select, SelectItem, SelectTrigger } from "../ui/select";
import { occasionList } from "./occasion";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { useEffect, useState } from "react";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { BookingFormValues, bookingSchema } from "@/lib/schema/booking";
import { zodResolver } from "@hookform/resolvers/zod";
import { CreateOrderRequest } from "@/api/models/order/create-order";
import { useUserStore } from "@/hooks/useUserStore";
import { createOrder } from "@/api/orderAPI";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

type props = {
  talentId: number | undefined;
};

export function BookingForm({ talentId }: props) {
  const methods = useForm<BookingFormValues>({
    resolver: zodResolver(bookingSchema),
    defaultValues: {
      recipient: "someone_else",
      pronoun: "",
    },
  });

  const router = useRouter();

  const {
    control,
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = methods;

  const [additionalFields, setAdditionalFields] = useState<any[]>([]);

  const typeValue = watch("type");
  useEffect(() => {
    const selected = occasionList.find((o) => o.name === typeValue)
    setAdditionalFields(selected?.additionalField ?? [])
  }, [typeValue]);

  const onSubmit = async (data: BookingFormValues) => {
    let detailData = data.request_details;
    let lines = [
      data.request_details?.trim(),
      `Xưng hô: ${data.pronoun}`,
      `Tên người nhận: ${data.name || useUserStore.getState().user?.name || "Không cung cấp"}`,
    ]

    if (data.additional) {
      for (const [key, value] of Object.entries(data.additional)) {
        if (value) {
          lines.push(`${key}: ${value}`)
        }
      }
    }

    let requestData: CreateOrderRequest = {
      type: data.type,
      talentId: talentId || 0,
      recipient: data.recipient,
      request_details: lines.join("\n"),
    };
    try {
      await createOrder(requestData);
      toast.success("Đặt dịch vụ thành công!");
      router.push("/don-hang");

    } catch (error) {
      toast.error("Đặt dịch vụ thất bại. Vui lòng thử lại sau.");
    }
  }

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className=""
      >
        {/* ----------- OCCASION ----------- */}
        <div className="space-y-2">
          <Label htmlFor="type">Chọn dịp</Label>
          <Controller
            control={control}
            name="type"
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Chọn dịp" />
                </SelectTrigger>
                <SelectContent>
                  {occasionList.map((o) => (
                    <SelectItem key={o.name} value={o.name}>
                      {o.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.type && (
            <p className="text-sm text-red-500">
              {errors.type.message}
            </p>
          )}
        </div>

        {/* ----------- RECIPIENT & NAME ----------- */}
        <div className="space-y-2">
          <Label>Video dành cho ai?</Label>
          <Controller
            control={control}
            name="recipient"
            render={({ field }) => (
              <RadioGroup
                className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                {...field}
                onValueChange={field.onChange}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="someone_else" id="someoneElse" />
                  <Label htmlFor="someone_else">Người khác</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="myself" id="myself" />
                  <Label htmlFor="myself">Bản thân tôi</Label>
                </div>
              </RadioGroup>
            )}
          />

          {watch("recipient") === "someone_else" && (
            <>
              <Input
                placeholder="Tên người nhận"
                {...register("name")}
              />
              {errors.name && (
                <p className="text-sm text-red-500">
                  {errors.name.message}
                </p>
              )}
            </>
          )}
        </div>

        {/* ----------- PRONOUN BUTTON GROUP ----------- */}
        <div className="space-y-2">
          <Label>Xưng hô</Label>
          <Controller
            control={control}
            name="pronoun"
            render={({ field }) => (
              <div className="flex flex-wrap gap-2">
                {["Anh", "Chị", "Bạn", "Mọi người", "Chú", "Cháu", "Con"].map(
                  (p) => (
                    <Button
                      type="button"
                      key={p}
                      variant={field.value === p ? "default" : "outline"}
                      onClick={() => field.onChange(p)}
                    >
                      {p}
                    </Button>
                  )
                )}
              </div>
            )}
          />
          {errors.pronoun && (
            <p className="text-sm text-red-500">
              {errors.pronoun.message}
            </p>
          )}
        </div>

        {/* ----------- MESSAGE ----------- */}
        <div className="space-y-2">
          <Label htmlFor="message">Thông tin chi tiết</Label>
          <Textarea
            rows={5}
            placeholder="Nhập tin nhắn của bạn"
            {...register("request_details", {
              required: "Thông tin chi tiết là bắt buộc",
            })}
          />
          {errors.request_details && (
            <p className="text-sm text-red-500">
              {errors.request_details.message}
            </p>
          )}
        </div>

        {/* ----------- ADDITIONAL FIELDS (dynamic) ----------- */}
        {additionalFields.length > 0 && (
          <div className="space-y-4">
            {additionalFields.map((field) => (
              <div key={field.name} className="space-y-1">
                <Label htmlFor={field.name}>{field.displayName}</Label>
                <Input
                  id={field.name}
                  type={field.type === "date" ? "date" : "text"}
                  placeholder={field.displayName}
                  {...register(`additional.${field.name}` as const, {
                    required: field.required
                      ? `${field.displayName} là bắt buộc`
                      : false,
                  })}
                />
                {errors.additional?.[field.name] && (
                  <p className="text-sm text-red-500">
                    {
                      (errors.additional as Record<string, any>)[field.name]
                        ?.message
                    }
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* ----------- SUBMIT ----------- */}
        <Button type="submit" className="w-full">
          Đặt dịch vụ
        </Button>
      </form>
    </FormProvider>
  )
}