import { ProfileFormData } from "@/app/admin/ho-so/page";
import api from "./axios";
import { Paginator } from "./models/paginator";
import { User } from "./models/user";

export const getUsers = async (
  page: number,
  limit: number
): Promise<Paginator<User>> => {
  const response = await api.get<Paginator<User>>("/users", {
    params: { page, limit },
  });
  return response.data;
};

export const fetchAllUsers = async (): Promise<User[]> => {
  const response = await api.get<User[]>("/users/all-user-list");
  return response.data;
};

export const createUser = async (userData: Partial<User>): Promise<User> => {
  const response = await api.post<User>("/users", userData);
  return response.data;
};

export const updateUser = async (userData: Partial<User>): Promise<User> => {
  const response = await api.patch<User>("/users", userData);
  return response.data;
};

export const deleteUser = async (userId: number): Promise<void> => {
  await api.delete("/users", { data: { id: userId } });
};

export const getUserById = async (id: number): Promise<User> => {
  const response = await api.get<User>(`/users/${id}`);
  return response.data;
};

export const getUserByNickName = async (nickName: string): Promise<User> => {
  const response = await api.get<User>(`/users/by-nickname/${nickName}`);
  return response.data;
};

export const lockUser = async (userId: number): Promise<User> => {
  const response = await api.patch<User>(`/users/lock/${userId}`);
  return response.data;
};
export const unlockUser = async (userId: number): Promise<User> => {
  const response = await api.patch<User>(`/users/unlock/${userId}`);
  return response.data;
};
export const getProfile = async () => {
  const response = await api.get<User>(`/auth/me`);
  return response.data;
};
export const updateProfile = async (body: ProfileFormData) => {
  const response = await api.patch<User>(`/auth/update-profile`, body, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};
