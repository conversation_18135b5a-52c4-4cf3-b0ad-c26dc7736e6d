import { ColumnDef } from "@tanstack/react-table"
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header"
import { Button } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { User } from "@/api/models/user"
import { Category } from "@/api/models/category"

type Props = {
  approveFunction?: (id: number) => Promise<void>;
  viewFunction?: (id: number) => void;
  disableFunction?: (id: number) => void;
}

export const getAdminTalentColumns = (props: Props): ColumnDef<User>[] => {
  return [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="ID" />
      ),
      cell: ({ row }) => <div className="font-mono text-xs">{row.getValue("id")}</div>,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tên Talent" />
      ),
      cell: ({ row }) => <div className="font-medium">{row.getValue("name")}</div>,
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => <div>{row.getValue("email")}</div>,
    },
    {
      accessorKey: "nick_name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Nick Name" />
      ),
      cell: ({ row }) => {
        const nickName = row.getValue("nick_name");
        return <div>{typeof nickName === "string" ? nickName : "—"}</div>;
      },
    },
    {
      accessorKey: "categories",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Danh mục" />
      ),
      cell: ({ row }) => <div>{(row.getValue("categories") as Category[] || []).map(o => o.name).join(", ")}</div>,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Trạng thái" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <div className={`capitalize font-semibold ${status === "active"
              ? "text-green-600"
              : status === "inactive"
                ? "text-gray-400"
                : status === "banned"
                  ? "text-red-600"
                  : "text-yellow-500"
            }`}>
            {status}
          </div>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "averageRating",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Đánh giá TB" />
      ),
      cell: ({ row }) => {
        const rating = row.getValue("averageRating") as number || 0;
        return <div>{rating.toFixed(1)} ★</div>
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Ngày tham gia" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"))
        return <div>{date.toLocaleDateString("vi-VN")}</div>
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const talent = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Mở menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Hành động</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(talent.id.toString())}>
                Sao chép ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => props.viewFunction?.(talent.id)}>
                Xem chi tiết
              </DropdownMenuItem>
              {(talent.status === "active" || talent.status === "pending") && (
                <DropdownMenuItem onClick={() => props.disableFunction?.(talent.id)}>
                  Vô hiệu hóa
                </DropdownMenuItem>
              )}
              {talent.status === "pending" && (
                <DropdownMenuItem onClick={() => props.approveFunction?.(talent.id)}>
                  Duyệt Talent
                </DropdownMenuItem>
              )}
              {talent.status === "inactive" && (
                <DropdownMenuItem onClick={() => props.approveFunction?.(talent.id)}>
                  Kích hoạt lại Talent
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
}


